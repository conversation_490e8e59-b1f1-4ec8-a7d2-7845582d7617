2025-07-30 13:37:40.589 [main] INFO  com.kingcola.imserver.BlogIMServerApplication - Starting BlogIMServerApplication using Java 11 on yuangang with PID 5272 (D:\WebProjects\Kingcola-Blog-System\blog-backend\kingcola-im-server\target\classes started by 35357 in D:\WebProjects\Kingcola-Blog-System\blog-backend)
2025-07-30 13:37:40.600 [main] INFO  com.kingcola.imserver.BlogIMServerApplication - The following 1 profile is active: "prod"
2025-07-30 13:37:42.914 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-30 13:37:42.918 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 13:37:42.969 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-07-30 13:37:43.647 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis.redisson-org.redisson.spring.starter.RedissonProperties' of type [org.redisson.spring.starter.RedissonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 13:37:43.661 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 13:37:43.667 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.redisson.spring.starter.RedissonAutoConfiguration' of type [org.redisson.spring.starter.RedissonAutoConfiguration$$EnhancerBySpringCGLIB$$299a828e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 13:37:43.830 [main] INFO  org.redisson.Version - Redisson 3.21.3
2025-07-30 13:37:48.088 [redisson-netty-2-6] INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for 115.190.101.180/115.190.101.180:6379
2025-07-30 13:37:50.495 [redisson-netty-2-19] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for 115.190.101.180/115.190.101.180:6379
2025-07-30 13:37:50.593 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisson' of type [org.redisson.Redisson] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 13:37:50.618 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redissonConnectionFactory' of type [org.redisson.spring.data.connection.RedissonConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 13:37:50.621 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.kingcola.config.RedisConfig$$EnhancerBySpringCGLIB$$fc5df9cb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-30 13:37:52.165 [main] INFO  com.kingcola.imserver.BlogIMServerApplication - Started BlogIMServerApplication in 12.348 seconds (JVM running for 15.307)
2025-07-30 13:37:52.306 [main] INFO  com.kingcola.imserver.netty.ws.WebSocketServer - websocket server 初始化完成,端口：8878
2025-07-30 13:40:50.891 [nioEventLoopGroup-8-1] INFO  com.kingcola.imserver.netty.IMChannelHandler - 005056fffec00001-00001498-0000002c-158acea24adb418b-d0a5ba2a连接
2025-07-30 13:40:51.065 [nioEventLoopGroup-8-1] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 接收到的登录数据: {accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0}
2025-07-30 13:40:51.203 [nioEventLoopGroup-8-1] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 转换后的登录信息: accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0
2025-07-30 13:40:51.204 [nioEventLoopGroup-8-1] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 接收到的登录数据: IMLoginInfo(accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0)
2025-07-30 13:40:51.207 [nioEventLoopGroup-8-1] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 转换后的登录信息: accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0
2025-07-30 13:40:51.207 [nioEventLoopGroup-8-1] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 开始验证token: 30717f95-8044-4f85-b224-efcb296b84de
2025-07-30 13:40:51.287 [nioEventLoopGroup-8-1] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - sa-token验证结果: loginId=1818
2025-07-30 13:40:51.288 [nioEventLoopGroup-8-1] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 用户登录，userId:1818
2025-07-30 13:40:52.509 [pool-2-thread-20] INFO  c.k.i.netty.processor.PrivateMessageProcessor - 接收到私聊消息，发送者:1818,接收者:1818，内容:{"type":30,"content":"true"}
2025-07-30 13:40:52.511 [pool-2-thread-20] INFO  c.k.i.netty.processor.PrivateMessageProcessor - 接收到私聊消息，发送者:1818,接收者:1818，内容:{"type":30,"content":"false"}
2025-07-30 13:40:52.586 [pool-2-thread-24] INFO  c.k.imserver.netty.processor.GroupMessageProcessor - 接收到群消息，发送者:1818,接收用户数量:1，内容:{"readedCount":0,"type":30,"content":"true"}
2025-07-30 13:40:52.587 [pool-2-thread-24] INFO  c.k.imserver.netty.processor.GroupMessageProcessor - 接收到群消息，发送者:1818,接收用户数量:1，内容:{"readedCount":0,"type":30,"content":"false"}
2025-07-30 13:50:22.652 [nioEventLoopGroup-8-1] INFO  com.kingcola.imserver.netty.IMChannelHandler - 断开连接,userId:1818,终端类型:0,005056fffec00001-00001498-0000002c-158acea24adb418b-d0a5ba2a
2025-07-30 13:58:59.436 [nioEventLoopGroup-8-2] INFO  com.kingcola.imserver.netty.IMChannelHandler - 005056fffec00001-00001498-0000002d-05481003fa6bde59-2240a0ab连接
2025-07-30 13:58:59.476 [nioEventLoopGroup-8-2] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 接收到的登录数据: {accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0}
2025-07-30 13:58:59.482 [nioEventLoopGroup-8-2] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 转换后的登录信息: accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0
2025-07-30 13:58:59.483 [nioEventLoopGroup-8-2] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 接收到的登录数据: IMLoginInfo(accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0)
2025-07-30 13:58:59.484 [nioEventLoopGroup-8-2] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 转换后的登录信息: accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0
2025-07-30 13:58:59.484 [nioEventLoopGroup-8-2] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 开始验证token: 30717f95-8044-4f85-b224-efcb296b84de
2025-07-30 13:58:59.529 [nioEventLoopGroup-8-2] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - sa-token验证结果: loginId=1818
2025-07-30 13:58:59.530 [nioEventLoopGroup-8-2] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 用户登录，userId:1818
2025-07-30 13:59:00.126 [pool-2-thread-9] INFO  c.k.i.netty.processor.PrivateMessageProcessor - 接收到私聊消息，发送者:1818,接收者:1818，内容:{"type":30,"content":"true"}
2025-07-30 13:59:00.291 [pool-2-thread-19] INFO  c.k.i.netty.processor.PrivateMessageProcessor - 接收到私聊消息，发送者:1818,接收者:1818，内容:{"type":30,"content":"false"}
2025-07-30 13:59:00.677 [pool-2-thread-23] INFO  c.k.imserver.netty.processor.GroupMessageProcessor - 接收到群消息，发送者:1818,接收用户数量:1，内容:{"readedCount":0,"type":30,"content":"true"}
2025-07-30 13:59:00.678 [pool-2-thread-23] INFO  c.k.imserver.netty.processor.GroupMessageProcessor - 接收到群消息，发送者:1818,接收用户数量:1，内容:{"readedCount":0,"type":30,"content":"false"}
2025-07-30 13:59:00.836 [nioEventLoopGroup-8-2] INFO  com.kingcola.imserver.netty.IMChannelHandler - 断开连接,userId:1818,终端类型:0,005056fffec00001-00001498-0000002d-05481003fa6bde59-2240a0ab
2025-07-30 13:59:04.809 [nioEventLoopGroup-8-3] INFO  com.kingcola.imserver.netty.IMChannelHandler - 005056fffec00001-00001498-0000002e-26a994c0ba6bf364-29fe861b连接
2025-07-30 13:59:04.859 [nioEventLoopGroup-8-3] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 接收到的登录数据: {accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0}
2025-07-30 13:59:04.860 [nioEventLoopGroup-8-3] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 转换后的登录信息: accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0
2025-07-30 13:59:04.862 [nioEventLoopGroup-8-3] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 接收到的登录数据: IMLoginInfo(accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0)
2025-07-30 13:59:04.862 [nioEventLoopGroup-8-3] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 转换后的登录信息: accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0
2025-07-30 13:59:04.863 [nioEventLoopGroup-8-3] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 开始验证token: 30717f95-8044-4f85-b224-efcb296b84de
2025-07-30 13:59:04.918 [nioEventLoopGroup-8-3] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - sa-token验证结果: loginId=1818
2025-07-30 13:59:04.919 [nioEventLoopGroup-8-3] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 用户登录，userId:1818
2025-07-30 13:59:06.019 [pool-2-thread-6] INFO  c.k.i.netty.processor.PrivateMessageProcessor - 接收到私聊消息，发送者:1818,接收者:1818，内容:{"type":30,"content":"true"}
2025-07-30 13:59:06.186 [pool-2-thread-1] INFO  c.k.i.netty.processor.PrivateMessageProcessor - 接收到私聊消息，发送者:1818,接收者:1818，内容:{"type":30,"content":"false"}
2025-07-30 13:59:06.499 [pool-2-thread-22] INFO  c.k.imserver.netty.processor.GroupMessageProcessor - 接收到群消息，发送者:1818,接收用户数量:1，内容:{"readedCount":0,"type":30,"content":"true"}
2025-07-30 13:59:06.502 [pool-2-thread-22] INFO  c.k.imserver.netty.processor.GroupMessageProcessor - 接收到群消息，发送者:1818,接收用户数量:1，内容:{"readedCount":0,"type":30,"content":"false"}
2025-07-30 14:02:36.665 [nioEventLoopGroup-8-3] INFO  com.kingcola.imserver.netty.IMChannelHandler - 断开连接,userId:1818,终端类型:0,005056fffec00001-00001498-0000002e-26a994c0ba6bf364-29fe861b
2025-07-30 14:09:22.502 [nioEventLoopGroup-8-4] INFO  com.kingcola.imserver.netty.IMChannelHandler - 005056fffec00001-00001498-0000002f-10d5ff4f33b16038-47900781连接
2025-07-30 14:09:22.567 [nioEventLoopGroup-8-4] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 接收到的登录数据: {accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0}
2025-07-30 14:09:22.573 [nioEventLoopGroup-8-4] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 转换后的登录信息: accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0
2025-07-30 14:09:22.574 [nioEventLoopGroup-8-4] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 接收到的登录数据: IMLoginInfo(accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0)
2025-07-30 14:09:22.575 [nioEventLoopGroup-8-4] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 转换后的登录信息: accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0
2025-07-30 14:09:22.576 [nioEventLoopGroup-8-4] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 开始验证token: 30717f95-8044-4f85-b224-efcb296b84de
2025-07-30 14:09:22.634 [nioEventLoopGroup-8-4] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - sa-token验证结果: loginId=1818
2025-07-30 14:09:22.634 [nioEventLoopGroup-8-4] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 用户登录，userId:1818
2025-07-30 14:09:23.556 [pool-2-thread-24] INFO  c.k.i.netty.processor.PrivateMessageProcessor - 接收到私聊消息，发送者:1818,接收者:1818，内容:{"type":30,"content":"true"}
2025-07-30 14:09:23.557 [pool-2-thread-20] INFO  c.k.imserver.netty.processor.GroupMessageProcessor - 接收到群消息，发送者:1818,接收用户数量:1，内容:{"readedCount":0,"type":30,"content":"true"}
2025-07-30 14:09:23.559 [pool-2-thread-24] INFO  c.k.i.netty.processor.PrivateMessageProcessor - 接收到私聊消息，发送者:1818,接收者:1818，内容:{"type":30,"content":"false"}
2025-07-30 14:09:23.758 [pool-2-thread-13] INFO  c.k.imserver.netty.processor.GroupMessageProcessor - 接收到群消息，发送者:1818,接收用户数量:1，内容:{"readedCount":0,"type":30,"content":"false"}
2025-07-30 14:11:54.503 [nioEventLoopGroup-8-4] INFO  com.kingcola.imserver.netty.IMChannelHandler - 心跳超时，即将断开连接,用户id:1818,终端类型:0 
2025-07-30 14:11:54.554 [nioEventLoopGroup-8-4] INFO  com.kingcola.imserver.netty.IMChannelHandler - 断开连接,userId:1818,终端类型:0,005056fffec00001-00001498-0000002f-10d5ff4f33b16038-47900781
2025-07-30 14:15:35.866 [nioEventLoopGroup-8-5] INFO  com.kingcola.imserver.netty.IMChannelHandler - 005056fffec00001-00001498-00000030-3ad8b3c3ecba92b6-831a25ab连接
2025-07-30 14:15:35.878 [nioEventLoopGroup-8-5] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 接收到的登录数据: {accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0}
2025-07-30 14:15:35.879 [nioEventLoopGroup-8-5] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 转换后的登录信息: accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0
2025-07-30 14:15:35.879 [nioEventLoopGroup-8-5] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 接收到的登录数据: IMLoginInfo(accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0)
2025-07-30 14:15:35.880 [nioEventLoopGroup-8-5] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 转换后的登录信息: accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0
2025-07-30 14:15:35.880 [nioEventLoopGroup-8-5] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 开始验证token: 30717f95-8044-4f85-b224-efcb296b84de
2025-07-30 14:15:35.920 [nioEventLoopGroup-8-5] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - sa-token验证结果: loginId=1818
2025-07-30 14:15:35.920 [nioEventLoopGroup-8-5] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 用户登录，userId:1818
2025-07-30 14:15:41.984 [pool-2-thread-12] INFO  c.k.i.netty.processor.PrivateMessageProcessor - 接收到私聊消息，发送者:1818,接收者:1818，内容:{"type":30,"content":"true"}
2025-07-30 14:15:42.139 [pool-2-thread-13] INFO  c.k.i.netty.processor.PrivateMessageProcessor - 接收到私聊消息，发送者:1818,接收者:1818，内容:{"type":30,"content":"false"}
2025-07-30 14:15:47.171 [pool-2-thread-3] INFO  c.k.imserver.netty.processor.GroupMessageProcessor - 接收到群消息，发送者:1818,接收用户数量:1，内容:{"readedCount":0,"type":30,"content":"true"}
2025-07-30 14:15:47.324 [pool-2-thread-19] INFO  c.k.imserver.netty.processor.GroupMessageProcessor - 接收到群消息，发送者:1818,接收用户数量:1，内容:{"readedCount":0,"type":30,"content":"false"}
2025-07-30 14:25:25.358 [nioEventLoopGroup-8-5] INFO  com.kingcola.imserver.netty.IMChannelHandler - 断开连接,userId:1818,终端类型:0,005056fffec00001-00001498-00000030-3ad8b3c3ecba92b6-831a25ab
2025-07-30 14:25:31.619 [nioEventLoopGroup-8-6] INFO  com.kingcola.imserver.netty.IMChannelHandler - 005056fffec00001-00001498-00000031-15f6826946c3a9dc-35265bf8连接
2025-07-30 14:25:31.639 [nioEventLoopGroup-8-6] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 接收到的登录数据: {accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0}
2025-07-30 14:25:31.642 [nioEventLoopGroup-8-6] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 转换后的登录信息: accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0
2025-07-30 14:25:31.643 [nioEventLoopGroup-8-6] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 接收到的登录数据: IMLoginInfo(accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0)
2025-07-30 14:25:31.643 [nioEventLoopGroup-8-6] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 转换后的登录信息: accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0
2025-07-30 14:25:31.643 [nioEventLoopGroup-8-6] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 开始验证token: 30717f95-8044-4f85-b224-efcb296b84de
2025-07-30 14:25:31.702 [nioEventLoopGroup-8-6] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - sa-token验证结果: loginId=1818
2025-07-30 14:25:31.703 [nioEventLoopGroup-8-6] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 用户登录，userId:1818
2025-07-30 14:25:32.363 [pool-2-thread-10] INFO  c.k.i.netty.processor.PrivateMessageProcessor - 接收到私聊消息，发送者:1818,接收者:1818，内容:{"type":30,"content":"true"}
2025-07-30 14:25:32.521 [pool-2-thread-1] INFO  c.k.i.netty.processor.PrivateMessageProcessor - 接收到私聊消息，发送者:1818,接收者:1818，内容:{"type":30,"content":"false"}
2025-07-30 14:25:32.720 [pool-2-thread-17] INFO  c.k.imserver.netty.processor.GroupMessageProcessor - 接收到群消息，发送者:1818,接收用户数量:1，内容:{"readedCount":0,"type":30,"content":"true"}
2025-07-30 14:25:32.722 [pool-2-thread-17] INFO  c.k.imserver.netty.processor.GroupMessageProcessor - 接收到群消息，发送者:1818,接收用户数量:1，内容:{"readedCount":0,"type":30,"content":"false"}
2025-07-30 14:39:48.243 [nioEventLoopGroup-8-6] INFO  com.kingcola.imserver.netty.IMChannelHandler - 断开连接,userId:1818,终端类型:0,005056fffec00001-00001498-00000031-15f6826946c3a9dc-35265bf8
2025-07-30 14:40:12.996 [nioEventLoopGroup-8-7] INFO  com.kingcola.imserver.netty.IMChannelHandler - 005056fffec00001-00001498-00000032-070f453bfa2d1cb0-50223df1连接
2025-07-30 14:40:13.019 [nioEventLoopGroup-8-7] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 接收到的登录数据: {accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0}
2025-07-30 14:40:13.025 [nioEventLoopGroup-8-7] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 转换后的登录信息: accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0
2025-07-30 14:40:13.026 [nioEventLoopGroup-8-7] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 接收到的登录数据: IMLoginInfo(accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0)
2025-07-30 14:40:13.027 [nioEventLoopGroup-8-7] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 转换后的登录信息: accessToken=30717f95-8044-4f85-b224-efcb296b84de, terminal=0
2025-07-30 14:40:13.027 [nioEventLoopGroup-8-7] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 开始验证token: 30717f95-8044-4f85-b224-efcb296b84de
2025-07-30 14:40:13.107 [nioEventLoopGroup-8-7] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - sa-token验证结果: loginId=1818
2025-07-30 14:40:13.107 [nioEventLoopGroup-8-7] INFO  c.kingcola.imserver.netty.processor.LoginProcessor - 用户登录，userId:1818
2025-07-30 14:40:14.137 [pool-2-thread-8] INFO  c.k.i.netty.processor.PrivateMessageProcessor - 接收到私聊消息，发送者:1818,接收者:1818，内容:{"type":30,"content":"true"}
2025-07-30 14:40:14.142 [pool-2-thread-8] INFO  c.k.i.netty.processor.PrivateMessageProcessor - 接收到私聊消息，发送者:1818,接收者:1818，内容:{"type":30,"content":"false"}
2025-07-30 14:40:14.286 [pool-2-thread-12] INFO  c.k.imserver.netty.processor.GroupMessageProcessor - 接收到群消息，发送者:1818,接收用户数量:1，内容:{"readedCount":0,"type":30,"content":"true"}
2025-07-30 14:40:14.286 [pool-2-thread-12] INFO  c.k.imserver.netty.processor.GroupMessageProcessor - 接收到群消息，发送者:1818,接收用户数量:1，内容:{"readedCount":0,"type":30,"content":"false"}
2025-07-30 14:50:08.019 [nioEventLoopGroup-8-7] INFO  com.kingcola.imserver.netty.IMChannelHandler - 心跳超时，即将断开连接,用户id:1818,终端类型:0 
2025-07-30 14:50:08.099 [nioEventLoopGroup-8-7] INFO  com.kingcola.imserver.netty.IMChannelHandler - 断开连接,userId:1818,终端类型:0,005056fffec00001-00001498-00000032-070f453bfa2d1cb0-50223df1
2025-07-30 15:01:52.861 [SpringApplicationShutdownHook] INFO  com.kingcola.mq.RedisMQPullTask - 消费线程停止...
2025-07-30 15:01:52.903 [SpringApplicationShutdownHook] INFO  com.kingcola.imserver.netty.ws.WebSocketServer - websocket server 停止
