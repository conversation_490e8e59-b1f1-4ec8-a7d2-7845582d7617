<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="dc5139ae-9320-46dd-a64b-f400df228662" name="Changes" comment="feat(article): 添加获取上一篇和下一篇文章的接口，优化文章管理功能，完善推荐算法的设置实现推荐文章和相似文章推荐功能">
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/annotation/RedisLock.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/aspect/RedisLockAspect.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/config/ICEServer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/config/WebrtcConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/contant/IMConstant.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/contant/IMRedisKey.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/FriendDndDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/GroupBanDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/GroupDndDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/GroupInviteDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/GroupMemberRemoveDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/GroupMessageDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/GroupUnbanDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/PrivateMessageDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/UserBanDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/WebrtcGroupAnswerDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/WebrtcGroupCandidateDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/WebrtcGroupDeviceDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/WebrtcGroupFailedDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/WebrtcGroupInviteDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/WebrtcGroupJoinDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/WebrtcGroupOfferDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/dto/im/WebrtcGroupSetupDTO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/entity/FileInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/entity/Friend.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/entity/Group.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/entity/GroupMember.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/entity/GroupMessage.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/entity/PrivateMessage.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/entity/SensitiveWord.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/entity/WebrtcUserInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/enums/FileType.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/enums/IMCmdType.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/enums/IMListenerType.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/enums/IMSendCode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/enums/IMTerminalType.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mapper/FileInfoMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mapper/FriendMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mapper/GroupMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mapper/GroupMemberMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mapper/GroupMessageMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mapper/PrivateMessageMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mapper/SensitiveWordMapper.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/model/IMGroupMessage.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/model/IMHeartbeatInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/model/IMLoginInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/model/IMPrivateMessage.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/model/IMRecvInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/model/IMSendInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/model/IMSendResult.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/model/IMSessionInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/model/IMSystemMessage.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/model/IMUserInfo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mq/RedisMQConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mq/RedisMQConsumer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mq/RedisMQListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mq/RedisMQPullTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mq/RedisMQTemplate.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/serializer/DateToLongSerializer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/utils/CommaTextUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/utils/FileUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/utils/ImageUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/utils/ThreadPoolExecutorFactory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/vo/im/FriendVO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/vo/im/GroupMemberVO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/vo/im/GroupMessageVO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/vo/im/GroupVO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/vo/im/OnlineTerminalVO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/vo/im/PrivateMessageVO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/vo/im/SystemConfigVO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/vo/im/SystemMessageVO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/vo/im/UploadImageVO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/vo/im/WebrtcGroupFailedVO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/vo/im/WebrtcGroupInfoVO.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/resources/library/ambiguity.dic" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/resources/library/ansj_stopword.dic" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/resources/library/ansj_user_dict.dic" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-common/src/main/resources/library/synonyms.dic" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-client/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-client/src/main/java/com/kingcola/IMAutoConfiguration.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-client/src/main/java/com/kingcola/IMClient.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-client/src/main/java/com/kingcola/annotation/IMListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-client/src/main/java/com/kingcola/listener/MessageListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-client/src/main/java/com/kingcola/listener/MessageListenerMulticaster.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-client/src/main/java/com/kingcola/sender/IMSender.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-client/src/main/java/com/kingcola/task/AbstractMessageResultTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-client/src/main/java/com/kingcola/task/GroupMessageResultResultTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-client/src/main/java/com/kingcola/task/PrivateMessageResultResultTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-client/src/main/java/com/kingcola/task/SystemMessageResultResultTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-client/src/main/resources/META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/annotation/OnlineCheck.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/annotation/RepeatSubmit.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/aspect/OnlineCheckAspect.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/aspect/RepeatSubmitAspect.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/config/TaskSchedulerConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/contant/Constant.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/contant/RedisKey.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/controller/FriendController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/controller/GroupController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/controller/GroupMessageController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/controller/PrivateMessageController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/controller/SystemController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/controller/UserController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/controller/WebrtcPrivateController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/enums/MessageStatus.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/enums/MessageType.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/enums/ResultCode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/enums/WebrtcMode.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/exception/GlobalException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/exception/GlobalExceptionHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/listener/GroupMessageListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/listener/PrivateMessageListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/listener/SystemMessageListener.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/result/Result.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/result/ResultUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/FriendService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/GroupMemberService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/GroupMessageService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/GroupService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/PrivateMessageService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/SensitiveWordService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/UserService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/WebrtcPrivateService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/impl/FriendServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/impl/GroupMemberServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/impl/GroupMessageServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/impl/GroupServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/impl/PrivateMessageServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/impl/SensitiveWordServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/impl/UserServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/service/impl/WebrtcPrivateServiceImpl.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/session/WebrtcGroupSession.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/session/WebrtcPrivateSession.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/task/consumer/GroupBannedConsumerTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/task/consumer/GroupUnbanConsumerTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/task/consumer/UserBannedConsumerTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/task/schedule/ReloadSensitiveWordTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/util/AiUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/util/BeanUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/util/DateTimeUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/util/SensitiveFilterUtil.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-platform/src/main/java/com/kingcola/implatform/util/UserStateUtils.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/pom.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/BlogIMServerApplication.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/constant/ChannelAttrKey.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/IMChannelHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/IMServer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/IMServerGroup.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/UserChannelCtxMap.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/processor/AbstractMessageProcessor.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/processor/GroupMessageProcessor.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/processor/HeartbeatProcessor.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/processor/LoginProcessor.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/processor/PrivateMessageProcessor.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/processor/ProcessorFactory.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/processor/SystemMessageProcessor.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/tcp/TcpSocketServer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/tcp/endecode/MessageProtocolDecoder.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/tcp/endecode/MessageProtocolEncoder.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/ws/WebSocketServer.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/ws/endecode/MessageProtocolDecoder.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/netty/ws/endecode/MessageProtocolEncoder.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/task/AbstractPullMessageTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/task/PullGroupMessageTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/task/PullPrivateMessageTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/task/PullSystemMessageTask.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/java/com/kingcola/imserver/util/SpringContextHolder.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/resources/application-dev.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/resources/application-prod.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/resources/application-test.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/resources/application.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/kingcola-im-server/src/main/resources/banner.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-admin/src/main/java/com/kingcola/service/impl/SysCategoryServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-admin/src/main/java/com/kingcola/service/impl/SysCategoryServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-admin/src/main/java/com/kingcola/service/impl/SysCommentServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-admin/src/main/java/com/kingcola/service/impl/SysCommentServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-admin/src/main/java/com/kingcola/service/impl/SysConfigServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-admin/src/main/java/com/kingcola/service/impl/SysConfigServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-admin/src/main/java/com/kingcola/service/impl/SysTagServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-admin/src/main/java/com/kingcola/service/impl/SysTagServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-admin/src/main/java/com/kingcola/service/impl/SysWebConfigServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-admin/src/main/java/com/kingcola/service/impl/SysWebConfigServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-api/src/main/java/com/kingcola/config/ArticleSearchIndexGenerator.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-api/src/main/java/com/kingcola/config/ArticleSearchIndexGenerator.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-api/src/main/java/com/kingcola/controller/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-api/src/main/java/com/kingcola/controller/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-api/src/main/java/com/kingcola/service/impl/ArticleServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-api/src/main/java/com/kingcola/service/impl/ArticleServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-api/src/main/java/com/kingcola/service/impl/HomeServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-api/src/main/java/com/kingcola/service/impl/HomeServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-api/src/main/java/com/kingcola/service/impl/MomentServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-api/src/main/java/com/kingcola/service/impl/MomentServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-auth/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-auth/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-auth/src/main/java/com/kingcola/config/xss/XssHttpServletRequestWrapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-auth/src/main/java/com/kingcola/config/xss/XssHttpServletRequestWrapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-auth/src/main/java/com/kingcola/service/impl/AuthServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-auth/src/main/java/com/kingcola/service/impl/AuthServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-common/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-common/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/common/Constants.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/common/Constants.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/common/RedisConstants.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/common/RedisConstants.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/config/RedisConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/config/RedisConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/config/WebMvcConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/config/WebMvcConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mapper/SysCommentMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mapper/SysCommentMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mapper/SysMomentMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/mapper/SysMomentMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/utils/ArticleSearchIndexUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/utils/ArticleSearchIndexUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/utils/IKAnalyzerUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/utils/IKAnalyzerUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/utils/RedisUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-common/src/main/java/com/kingcola/utils/RedisUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-common/src/main/resources/IKAnalyzer.cfg.xml" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-common/src/main/resources/IKAnalyzer.cfg.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-common/src/main/resources/custom/kingcola_dict.dic" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-common/src/main/resources/custom/kingcola_dict.dic" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-common/src/main/resources/custom/kingcola_stopword.dic" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-common/src/main/resources/custom/kingcola_stopword.dic" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-file/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-file/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-server/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-server/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-server/src/main/java/com/kingcola/BlogAdminApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-server/src/main/java/com/kingcola/BlogAdminApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-server/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-server/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-server/src/main/resources/application-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-server/src/main/resources/application-prod.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-server/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-server/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-server/src/main/resources/mapper/SysCommentMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-server/src/main/resources/mapper/SysCommentMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-server/src/main/resources/mapper/SysMomentMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-server/src/main/resources/mapper/SysMomentMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/kingcola-server/src/main/resources/mapper/SysUserMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/kingcola-server/src/main/resources/mapper/SysUserMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/kingcola-blog.sql" beforeDir="false" afterPath="$PROJECT_DIR$/sql/kingcola-blog.sql" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\ApplicationFolder\apache-maven-3.6.1-bin\apache-maven-3.6.1" />
        <option name="localRepository" value="D:\ApplicationFolder\apache-maven-3.6.1-bin\apache-maven-3.6.1\mvn_repo" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\ApplicationFolder\apache-maven-3.6.1-bin\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="11" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2uhXhPoMvdloUYSIQrfhObApzx3" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="autoscrollToSource" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;JUnit.SegmentationTest.testAnsjMultiModeSegmentation.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testAnsjOptimizationComparison.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testAnsjOptimizedBlogSegmentation.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testAnsjOptimizedSegmentation.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testBasicSegmentation.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testBlogContentOptimizedSegmentation.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testBlogTechSegmentationOptimized.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testComprehensiveAnsjOptimization.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testComprehensiveComparison.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testDictionaryLoading.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testHighPerformanceSegmentation.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testOptimizedAnsjSegmentation.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testPerformanceComparison.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testSceneBasedSegmentation.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testSegmentationIntelligenceComparison.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testSegmentationPerformanceComparison.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testStopWordFiltering.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testTechTermRecognition.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.SegmentationTest.testUserDictionary.executor&quot;: &quot;Run&quot;,
    &quot;Maven.blog-backend [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.blog-backend [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.blog-backend [package].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.BlogAdminApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.BlogIMServerApp.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.BlogIMServerApplication.executor&quot;: &quot;Run&quot;,
    &quot;com.codeium.enabled&quot;: &quot;true&quot;,
    &quot;com.codeium.snoozedEndTime&quot;: &quot;1743160716433&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/WebProjects/Kingcola-Blog-System/blog-backend/kingcola-common/src/main/resources/library&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.lookFeel&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql_aurora_aws&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\WebProjects\Kingcola-Blog-System\blog-backend\kingcola-common\src\main\resources\library" />
      <recent name="D:\WebProjects\Kingcola-Blog-System\blog-backend\kingcola-im-server\src\main\resources" />
      <recent name="D:\WebProjects\Kingcola-Blog-System\blog-backend\kingcola-common\src\main\java\com\kingcola\annotation" />
      <recent name="D:\WebProjects\Kingcola-Blog-System\blog-backend\kingcola-im-platform\src\main\java\com\kingcola\implatform" />
      <recent name="D:\WebProjects\Kingcola-Blog-System\blog-backend\kingcola-common\src\main\java\com\kingcola\mapper" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\WebProjects\Kingcola-Blog-System\blog-backend\kingcola-common" />
      <recent name="D:\WebProjects\Kingcola-Blog-System\blog-backend\kingcola-server\src\main\resources\static\images\captcha" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.kingcola.implatform.util" />
      <recent name="com.kingcola.implatform.service" />
      <recent name="com.kingcola.common" />
      <recent name="com.kingcola" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.BlogAdminApplication">
    <configuration name="SegmentationTest.testAnsjMultiModeSegmentation" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="kingcola-common" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.kingcola.utils.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.kingcola.utils" />
      <option name="MAIN_CLASS_NAME" value="com.kingcola.utils.SegmentationTest" />
      <option name="METHOD_NAME" value="testAnsjMultiModeSegmentation" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SegmentationTest.testBlogTechSegmentationOptimized" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="kingcola-common" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.kingcola.utils.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.kingcola.utils" />
      <option name="MAIN_CLASS_NAME" value="com.kingcola.utils.SegmentationTest" />
      <option name="METHOD_NAME" value="testBlogTechSegmentationOptimized" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SegmentationTest.testHighPerformanceSegmentation" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="kingcola-common" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.kingcola.utils.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.kingcola.utils" />
      <option name="MAIN_CLASS_NAME" value="com.kingcola.utils.SegmentationTest" />
      <option name="METHOD_NAME" value="testHighPerformanceSegmentation" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SegmentationTest.testSegmentationIntelligenceComparison" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="kingcola-common" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.kingcola.utils.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.kingcola.utils" />
      <option name="MAIN_CLASS_NAME" value="com.kingcola.utils.SegmentationTest" />
      <option name="METHOD_NAME" value="testSegmentationIntelligenceComparison" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SegmentationTest.testStopWordFiltering" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="kingcola-common" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.kingcola.utils.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.kingcola.utils" />
      <option name="MAIN_CLASS_NAME" value="com.kingcola.utils.SegmentationTest" />
      <option name="METHOD_NAME" value="testStopWordFiltering" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JUnit" factoryName="JUnit">
      <shortenClasspath name="ARGS_FILE" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BlogAdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="kingcola-server" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kingcola.BlogAdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BlogIMServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="kingcola-im-server" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kingcola.imserver.BlogIMServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.SegmentationTest.testSegmentationIntelligenceComparison" />
        <item itemvalue="JUnit.SegmentationTest.testHighPerformanceSegmentation" />
        <item itemvalue="JUnit.SegmentationTest.testBlogTechSegmentationOptimized" />
        <item itemvalue="JUnit.SegmentationTest.testStopWordFiltering" />
        <item itemvalue="JUnit.SegmentationTest.testAnsjMultiModeSegmentation" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="dc5139ae-9320-46dd-a64b-f400df228662" name="Changes" comment="" />
      <created>1742701900715</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1742701900715</updated>
      <workItem from="1742701902132" duration="8589000" />
      <workItem from="1742726079616" duration="959000" />
      <workItem from="1742824296328" duration="48012000" />
      <workItem from="1743007518176" duration="31466000" />
      <workItem from="1743093546135" duration="89000" />
      <workItem from="1743093667307" duration="382000" />
      <workItem from="1743120368403" duration="9893000" />
      <workItem from="1743157220513" duration="338000" />
      <workItem from="1743157581805" duration="7853000" />
      <workItem from="1743228277779" duration="6574000" />
      <workItem from="1743235216427" duration="24246000" />
      <workItem from="1743269404710" duration="289000" />
      <workItem from="1743304797712" duration="52856000" />
      <workItem from="1743469120293" duration="52404000" />
      <workItem from="1743669011273" duration="678000" />
      <workItem from="1743755650168" duration="7416000" />
      <workItem from="1743847885623" duration="19000" />
      <workItem from="1743849579441" duration="9928000" />
      <workItem from="1744295349794" duration="1224000" />
      <workItem from="1744441384953" duration="601000" />
      <workItem from="1746491494570" duration="1789000" />
      <workItem from="1746501909515" duration="1577000" />
      <workItem from="1746510098726" duration="8622000" />
      <workItem from="1746632798987" duration="21191000" />
      <workItem from="1747208386973" duration="208000" />
      <workItem from="1747237766395" duration="3082000" />
      <workItem from="1747578765997" duration="86000" />
      <workItem from="1747585670554" duration="269000" />
      <workItem from="1747615461846" duration="64000" />
      <workItem from="1748587080432" duration="621000" />
      <workItem from="1748601739493" duration="35000" />
      <workItem from="1749556248017" duration="52740000" />
      <workItem from="1749813787467" duration="23000" />
      <workItem from="1750069786455" duration="12320000" />
      <workItem from="1750428845864" duration="210000" />
      <workItem from="1750429100057" duration="254000" />
      <workItem from="1750429392405" duration="108000" />
      <workItem from="1750429539092" duration="165000" />
      <workItem from="1750429743820" duration="512000" />
      <workItem from="1750430316533" duration="3045000" />
      <workItem from="1750686975752" duration="616000" />
      <workItem from="1751015608060" duration="17575000" />
      <workItem from="1751188588886" duration="463000" />
      <workItem from="1751189331924" duration="45584000" />
      <workItem from="1751336724877" duration="20756000" />
      <workItem from="1751423222387" duration="388000" />
      <workItem from="1751423821883" duration="124000" />
      <workItem from="1751423961973" duration="164000" />
      <workItem from="1751424260707" duration="57000" />
      <workItem from="1751424346675" duration="704000" />
      <workItem from="1751425089563" duration="147000" />
      <workItem from="1751425268085" duration="201000" />
      <workItem from="1751425498621" duration="51062000" />
      <workItem from="1751536735123" duration="15711000" />
      <workItem from="1751597497200" duration="32000" />
      <workItem from="1751597606226" duration="31000" />
      <workItem from="1751597807649" duration="50000" />
      <workItem from="1751597884740" duration="3396000" />
      <workItem from="1751601709084" duration="10512000" />
      <workItem from="1751636363917" duration="41820000" />
      <workItem from="1751815132666" duration="20017000" />
      <workItem from="1751884237702" duration="95260000" />
      <workItem from="1752289520899" duration="12277000" />
      <workItem from="1752392213278" duration="47735000" />
      <workItem from="1752559026165" duration="21413000" />
      <workItem from="1752669639049" duration="9206000" />
      <workItem from="1752724003913" duration="137000" />
      <workItem from="1752724211124" duration="50000" />
      <workItem from="1752724767978" duration="152000" />
      <workItem from="1752724979442" duration="705000" />
      <workItem from="1752725968855" duration="1739000" />
      <workItem from="1752730091865" duration="203000" />
      <workItem from="1752730303664" duration="19000" />
      <workItem from="1752730457046" duration="987000" />
      <workItem from="1752743370532" duration="132000" />
      <workItem from="1752743529260" duration="18583000" />
      <workItem from="1752828107804" duration="10430000" />
      <workItem from="1752913193616" duration="7580000" />
      <workItem from="1752938441532" duration="38692000" />
      <workItem from="1753254324830" duration="88409000" />
      <workItem from="1753605575804" duration="34737000" />
      <workItem from="1753871296385" duration="485000" />
      <workItem from="1753891725094" duration="17912000" />
    </task>
    <task id="LOCAL-00001" summary="✨ 初始化提交：KingCola-ICG博客平台后端基础架构搭建完成&#10;&#10;🌟 核心功能：&#10;🔐 多元化认证（本地+GitHub+Gitee...）&#10;👮‍♂️ Sa-Token权限管理系统&#10;🔧 MyBatis-Plus数据处理&#10;📱 微信公众号扫码登录集成&#10;🧠 豆包AI内容生成&#10;📊 数据导出与统计&#10;📝 完整的日志追踪">
      <option name="closed" value="true" />
      <created>1749557945753</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1749557945753</updated>
    </task>
    <task id="LOCAL-00002" summary="✨ 增强文章管理功能，添加文章状态字段及相关逻辑，优化阅读量统计">
      <option name="closed" value="true" />
      <created>1749813107033</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1749813107033</updated>
    </task>
    <task id="LOCAL-00003" summary="feat(api): 添加博客贡献度排行功能并优化说说相关接口- 新增博客贡献度排行接口，根据文章数量、浏览量和点赞数计算用户贡献度&#10;- 优化说说相关接口，增加获取说说详情功能&#10;-调整首页相关接口，增加获取网站配置和热搜的功能&#10;- 优化 Nginx 配置文件，提高性能和安全性&#10;- 添加 Brotli 模块以支持更高效的压缩&#10;- 解决 CDN 缓存导致的页面加载问题">
      <option name="closed" value="true" />
      <created>1751212425411</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1751212425411</updated>
    </task>
    <task id="LOCAL-00004" summary="feat(auth): 实现多因素认证和设备验证功能&#10;&#10;- 新增 MFA 登录和设备验证流程&#10;- 实现预授权令牌机制&#10;- 优化密码加密和验证逻辑&#10;- 增加用户可信设备管理&#10;- 改进邮箱验证码发送和验证&#10;- 结合多因素登录优化确保用户安全&#10;- 新增XSS相关安全配置内容&#10;- 重构登录相关接口和数据结构">
      <option name="closed" value="true" />
      <created>1751294048169</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1751294048169</updated>
    </task>
    <task id="LOCAL-00005" summary="feat(profile): 添加用户公开信息接口，支持获取用户资料和预览卡片&#10;feat(file): 新增文件处理状态字段，优化文件管理逻辑&#10;feat(article): 实现文章集成Redis来优化全文搜索功能，支持关键词搜索和分页">
      <option name="closed" value="true" />
      <created>1751815896596</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1751815896596</updated>
    </task>
    <task id="LOCAL-00006" summary="feat(article): 添加获取上一篇和下一篇文章的接口，优化文章管理功能，完善推荐算法的设置实现推荐文章和相似文章推荐功能">
      <option name="closed" value="true" />
      <created>1752156829696</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752156829696</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="✨ 初始化提交：KingCola-ICG博客平台后端基础架构搭建完成&#10;&#10; 核心功能：&#10; 多元化认证（本地+GitHub+Gitee...）&#10;‍♂️ Sa-Token权限管理系统&#10; MyBatis-Plus数据处理&#10; 微信公众号扫码登录集成&#10; 豆包AI内容生成&#10; 数据导出与统计&#10; 完整的日志追踪" />
    <MESSAGE value="✨ 增强文章管理功能，添加文章状态字段及相关逻辑，优化阅读量统计" />
    <MESSAGE value="feat(api): 添加博客贡献度排行功能并优化说说相关接口- 新增博客贡献度排行接口，根据文章数量、浏览量和点赞数计算用户贡献度&#10;- 优化说说相关接口，增加获取说说详情功能&#10;-调整首页相关接口，增加获取网站配置和热搜的功能&#10;- 优化 Nginx 配置文件，提高性能和安全性&#10;- 添加 Brotli 模块以支持更高效的压缩&#10;- 解决 CDN 缓存导致的页面加载问题" />
    <MESSAGE value="feat(auth): 实现多因素认证和设备验证功能&#10;&#10;- 新增 MFA 登录和设备验证流程&#10;- 实现预授权令牌机制&#10;- 优化密码加密和验证逻辑&#10;- 增加用户可信设备管理&#10;- 改进邮箱验证码发送和验证&#10;- 结合多因素登录优化确保用户安全&#10;- 新增XSS相关安全配置内容&#10;- 重构登录相关接口和数据结构" />
    <MESSAGE value="feat(profile): 添加用户公开信息接口，支持获取用户资料和预览卡片&#10;feat(file): 新增文件处理状态字段，优化文件管理逻辑&#10;feat(article): 实现文章集成Redis来优化全文搜索功能，支持关键词搜索和分页" />
    <MESSAGE value="feat(article): 添加获取上一篇和下一篇文章的接口，优化文章管理功能，完善推荐算法的设置实现推荐文章和相似文章推荐功能" />
    <option name="LAST_COMMIT_MESSAGE" value="feat(article): 添加获取上一篇和下一篇文章的接口，优化文章管理功能，完善推荐算法的设置实现推荐文章和相似文章推荐功能" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>