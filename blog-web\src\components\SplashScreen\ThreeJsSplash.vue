<!-- 使用Three.js实现动态感的三维效果 -->
<template>
  <div class="splash-screen" :class="{ 'fade-out': startFadeOut }" v-show="visible">
    <div id="three-container" ref="threeContainer"></div>

    <div class="splash-content">
      <div class="logo-container">
        <div class="logo-circle">
          <img src="https://kingcola-blog.oss-cn-hangzhou.aliyuncs.com/favicon.ico" alt="Logo"
            class="logo-image" />
        </div>
        <div class="logo-text-wrapper">
          <div class="logo-text">KingCola-ICG Blog</div>
          <div class="logo-text-shadow">KingCola-ICG Blog</div>
        </div>
      </div>

      <div class="loading-bar">
        <div class="loading-progress" :style="{ width: `${loadingProgress}%` }"></div>
        <div class="loading-text">{{ Math.round(loadingProgress) }}%</div>
      </div>

      <div class="tagline">
        <div class="tagline-text">探索技术的无限可能</div>
        <div class="tagline-underline"></div>
      </div>

      <div class="scroll-indicator" v-show="loadingComplete">
        <span>向下滑动进入博客</span>
        <div class="arrow-down bounce"></div>
      </div>
    </div>

    <div class="tech-keywords">
      <div v-for="(keyword, index) in techKeywords" :key="index" class="keyword" :style="getKeywordStyle(index)">
        {{ keyword }}
      </div>
    </div>
  </div>
</template>

<script>
import * as THREE from 'three';
import { FontLoader } from 'three/examples/jsm/loaders/FontLoader.js';
import { TextGeometry } from 'three/examples/jsm/geometries/TextGeometry.js';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js';
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass.js';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import { gsap } from 'gsap';
import Stats from 'three/examples/jsm/libs/stats.module.js';

export default {
  name: 'ThreeJsSplash',
  data() {
    return {
      stats: null, // 性能监控
      fpsCheckInterval: null, // FPS检查定时器
      lowFPSCount: 0, // 低帧率计数
      hasReducedQuality: false, // 是否已降低质量
      lastFPSTime: 0, // 用于手动计算FPS
      frameCount: 0, // 用于手动计算FPS
      currentFPS: 60, // 用于手动计算FPS
      // visible: true,
      // 初始值从localStorage读取，而不是默认显示
      visible: this.shouldShowSplash(),
      loadingProgress: 0,
      loadingComplete: false,
      hasBeenShown: this.getHasBeenShown(), // 从localStorage读取是否显示过
      startFadeOut: false,
      isFadingOut: false, //  用于防止重复触发退出动画
      scrollListener: null,
      scene: null,
      camera: null,
      renderer: null,
      composer: null,
      particles: null,
      galaxyParticles: null,
      lines: null,
      textGroup: null, // 用于容纳所有3D文字相关的对象
      textMeshes: [], // 3D文字网格
      textGlows: [], // 文字光晕效果
      textFlows: [], // 文字流光效果
      textOutlines: [], // 文字轮廓
      textSideLights: [], // 文字侧边光
      font: null, // 存储加载的字体
      animationFrame: null,
      clock: null,
      raycaster: null,
      mouse: new THREE.Vector2(),
      smoothedMouse: new THREE.Vector2(), // 用于平滑鼠标移动
      interactiveObjects: [],
      techKeywords: [
        'Vue', 'React', 'JavaScript', 'TypeScript', 'Node.js',
        'Python', 'Java', 'Spring', 'Docker', 'Kubernetes',
        'AI', 'Machine Learning', 'Cloud', 'DevOps', 'HTML',
        'DeepSeek', 'ChatGPT', 'CSS', 'MySQL', 'RESTful API',
        'Web', 'Three.js', 'Redis', 'MongoDB', 'SpringBoot',
        'SpringCloud', 'SpringSecurity', 'MyBatis', 'MyBatisPlus',
        'SpringMVC', 'JVM', 'Nginx', 'Vite', '火山引擎服务器', 'Element-Plus', 'Ant Design',
        'Maven', 'Elasticsearch', 'RabbitMQ', 'Kafka', 'Nacos', 'Sentinel', 'Seata', 'Dubbo',
        'OAuth2', 'JWT', 'HTTPS', 'SSL/TLS', 'IntelliJ IDEA', 'Postman',
        'WebSocket', 'AIoT', 'GitHub', 'Gitee', 'Linux'
      ]
    };
  },
  // 在created生命周期中
  created() {
    // 检查是否是刷新页面
    const pageLoadTime = Date.now();
    const lastVisitTime = parseInt(localStorage.getItem('lastVisitTime') || '0');

    // 如果距离上次访问时间超过一定阈值（例如1分钟），认为是离开后返回
    // 否则认为是普通刷新，不需要显示开屏动画
    const isReturningVisit = pageLoadTime - lastVisitTime > 60 * 1000;

    if (isReturningVisit && localStorage.getItem('wasHidden') === 'true') {
      // 显示开屏动画
      // 但我们需要等到组件挂载后才能执行
      this.$nextTick(() => {
        this.showSplashScreen();
      });
    }

    // 更新最后访问时间
    localStorage.setItem('lastVisitTime', pageLoadTime.toString());

    // 清除标记
    localStorage.removeItem('wasHidden');
  },
  mounted() {
    this.clock = new THREE.Clock();
    this.lastFPSTime = performance.now();

    // 初始化性能监控（只在开屏动画显示时可见）
    if (this.visible) {
      this.stats = new Stats();
      this.stats.dom.style.position = 'absolute';
      this.stats.dom.style.right = '10px'; // 放在右侧
      this.stats.dom.style.bottom = '10px'; // 放在底部
      this.stats.dom.style.left = 'auto'; // 清除左侧定位
      this.stats.dom.style.top = 'auto'; // 清除顶部定位
      this.stats.dom.style.zIndex = '10000'; // 确保在最上层
      this.stats.dom.style.opacity = '0.7'; // 半透明
      this.stats.dom.style.transform = 'scale(0.8)'; // 稍微缩小
      this.stats.dom.style.transformOrigin = 'bottom right'; // 变换原点
      this.stats.dom.id = 'splash-fps-monitor'; // 添加ID以便于控制
      document.body.appendChild(this.stats.dom);
    }

    // 设置当前页面是否为首页
    this.isHomePage = this.$route && this.$route.path === '/';

    // 监听logo点击事件
    this.setupLogoClickListener();

    // 如果开屏显示，则禁用页面滚动
    if (this.visible) {
      this.disableBodyScroll();
    } else {
      this.enableBodyScroll();
    }

    // 设置性能监控
    this.fpsCheckInterval = setInterval(() => this.checkPerformance(), 1000);

    // 添加页面可见性变化事件监听
    document.addEventListener('visibilitychange', this.handleVisibilityChange);

    this.initThree();
    this.createStarField();
    this.createGalaxy();
    this.createParticles();
    this.createLines();
    this.createGrid();
    this.createHolographicSphere();
    this.create3DText();
    this.createDataCubes();
    this.createEnergyRings();
    this.createFloatingIcons();
    this.createDataStreamLines(); // 添加数据流线
    this.createFlowingEdges(); // 新增页面边缘流动线条
    this.createHUDElements(); // 添加HUD界面元素
    this.setupPostProcessing();
    this.animate();
    this.simulateLoading();

    this.scrollListener = this.handleScroll.bind(this);
    window.addEventListener('wheel', this.scrollListener);
    window.addEventListener('touchmove', this.scrollListener);
    window.addEventListener('mousemove', this.onMouseMove);
    window.addEventListener('resize', this.onWindowResize);
    // 添加浏览器关闭事件监听
    window.addEventListener('beforeunload', this.handleBeforeUnload);

    // 文字动画效果
    const logoText = this.$el.querySelector('.logo-text');
    const text = logoText.textContent;
    logoText.innerHTML = ''; // 清空内容
    text.split('').forEach((char, index) => {
      const span = document.createElement('span');
      span.textContent = char;
      span.style.setProperty('--i', index);
      logoText.appendChild(span);
    });

    gsap.from('.logo-text span', {
      opacity: 0,
      y: 20,
      stagger: 0.05,
      duration: 1,
      ease: 'power2.out',
    });

    gsap.from('.char-glow', {
      opacity: 0,
      scale: 0.5,
      stagger: 0.03,
      duration: 0.8,
      ease: 'back.out',
      delay: 0.5
    });

    // 关键词动画
    gsap.from('.keyword', {
      opacity: 0,
      scale: 0,
      stagger: 0.1,
      duration: 1,
      ease: 'elastic.out',
      delay: 1
    });
  },
  beforeDestroy() {
    window.removeEventListener('wheel', this.scrollListener);
    window.removeEventListener('touchmove', this.scrollListener);
    window.removeEventListener('mousemove', this.onMouseMove);
    window.removeEventListener('resize', this.onWindowResize);

    if (this.fpsCheckInterval) {
      clearInterval(this.fpsCheckInterval);
    }

    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
    }

    // 移除性能监控
    if (this.stats && this.stats.dom && this.stats.dom.parentElement) {
      this.stats.dom.parentElement.removeChild(this.stats.dom);
    }

    // 移除页面可见性变化事件监听
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);

    // 移除浏览器关闭事件监听
    window.removeEventListener('beforeunload', this.handleBeforeUnload);

    // 移除logo点击事件
    this.removeLogoClickListener();

    this.enableBodyScroll();
  },
  methods: {
    // 禁用页面滚动
    disableBodyScroll() {
      document.documentElement.classList.add('splash-screen-active');
    },

    // 启用页面滚动
    enableBodyScroll() {
      document.documentElement.classList.remove('splash-screen-active');
    },

    initThree() {
      this.scene = new THREE.Scene();
      this.scene.fog = new THREE.FogExp2(0x0a1a2a, 0.0015);

      // 优化相机设置以提高性能
      this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 1, 800); // 增大近平面距离，减小远平面距离
      this.camera.position.z = 30;

      this.renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true,
        powerPreference: 'high-performance',
        precision: 'mediump' // 使用中等精度以提高性能
      });
      this.renderer.setSize(window.innerWidth, window.innerHeight);
      this.renderer.setClearColor(0x050a20, 1);
      this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5)); // 限制最大像素比，提高性能
      this.renderer.shadowMap.enabled = false; // 关闭阴影以提高性能，开屏界面效果不受影响
      // this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
      this.$refs.threeContainer.appendChild(this.renderer.domElement);

      // 添加多种光源
      const ambientLight = new THREE.AmbientLight(0x222233, 0.5);
      this.scene.add(ambientLight);

      const pointLight = new THREE.PointLight(0x4fc3f7, 1, 100);
      pointLight.position.set(0, 10, 30);
      pointLight.castShadow = true;
      this.scene.add(pointLight);

      const blueLight = new THREE.PointLight(0x0d47a1, 2, 50);
      blueLight.position.set(-20, -10, 20);
      this.scene.add(blueLight);

      const purpleLight = new THREE.PointLight(0x9c27b0, 2, 50);
      purpleLight.position.set(20, 10, 20);
      this.scene.add(purpleLight);

      // 初始化射线检测器
      this.raycaster = new THREE.Raycaster();
    },

    // 处理页面关闭
    handleBeforeUnload() {
      localStorage.setItem('wasHidden', 'true');
    },

    // 处理页面可见性变化，优化用户体验并管理动画生命周期
    handleVisibilityChange() {
      if (document.visibilityState === 'hidden') {
        // 当页面隐藏时，暂停动画以节省资源
        if (this.animationFrame) {
          cancelAnimationFrame(this.animationFrame);
          this.animationFrame = null;
        }
        // 标记用户离开了页面
        localStorage.setItem('wasHidden', 'true');
      } else if (document.visibilityState === 'visible') {
        // 当页面重新可见时，如果开屏动画是活动的，则恢复动画
        if (this.visible && !this.animationFrame) {
          this.animate();
        }

        // --- 以下是处理"返回页面时重新显示开屏"的逻辑 ---
        const wasHidden = localStorage.getItem('wasHidden') === 'true';
        if (!wasHidden) return; // 如果不是从隐藏状态恢复，则不执行后续逻辑

        const now = Date.now();
        const lastShown = parseInt(localStorage.getItem('lastShownTime') || '0');
        const minInterval = 5 * 60 * 1000; // 5分钟

        // 无论如何，先清除"已隐藏"标记，防止意外的重复触发
        localStorage.removeItem('wasHidden');

        if (now - lastShown > minInterval) {
          // 间隔时间足够长，重新显示开屏动画
          localStorage.setItem('lastPath', this.$route.fullPath);
          localStorage.setItem('lastShownTime', now.toString());
          this.showSplashScreen();
        }
      }
    },

    // 检查是否应该显示开屏动画
    shouldShowSplash() {
      const hasShownBefore = localStorage.getItem('splashScreenShown') === 'true';
      // 只有第一次访问时显示开屏动画
      return !hasShownBefore;
    },

    // 获取是否已经显示过开屏动画
    getHasBeenShown() {
      return localStorage.getItem('splashScreenShown') === 'true';
    },

    // 设置已经查看过开屏动画的标志
    setSplashShown() {
      localStorage.setItem('splashScreenShown', 'true');
      this.hasBeenShown = true;
    },

    // 设置logo点击事件监听
    setupLogoClickListener() {
      // 延迟添加，确保DOM已经渲染
      this.$nextTick(() => {
        const logoElements = document.querySelectorAll('.logo img, .logo-text');
        logoElements.forEach(element => {
          if (element) {
            element.addEventListener('click', this.handleLogoClick);
          }
        });
      });
    },

    // 移除logo点击事件监听
    removeLogoClickListener() {
      const logoElements = document.querySelectorAll('.logo img, .logo-text');
      logoElements.forEach(element => {
        if (element) {
          element.removeEventListener('click', this.handleLogoClick);
        }
      });
    },

    // 处理logo点击事件
    handleLogoClick(event) {
      // 阻止默认的链接跳转行为
      event.preventDefault();
      event.stopPropagation();

      // 显示开屏动画
      this.showSplashScreen();

      return false;
    },

    // 显示开屏动画时记住当前路径
    showSplashScreen() {
      // 发出事件
      this.$root.$emit('splash-shown');
      // 如果没有保存路径，则保存当前路径
      if (!localStorage.getItem('lastPath')) {
        localStorage.setItem('lastPath', this.$route.fullPath);
      }

      // --- 核心修复：重置因退出动画而改变的状态 ---
      // 0. 恢复UI元素
      gsap.to('.splash-content, .tech-keywords', { opacity: 1, duration: 0.2 });
      // 1. 重置Three.js容器不透明度
      if (this.$refs.threeContainer) {
        gsap.to(this.$refs.threeContainer, { opacity: 1, duration: 0 });
      }
      // 2. 重置相机位置
      if (this.camera) {
        this.camera.position.set(0, 0, 30);
        this.camera.lookAt(this.scene.position);
      }
      // 3. 重置雾效浓度
      if (this.scene && this.scene.fog) {
        this.scene.fog.density = 0.0015;
      }
      // 4. 重置GSAP动画元素初始状态，以便动画可以重新播放
      gsap.set('.glow-letter, .keyword', {
        opacity: 0,
        y: 0,
        scale: 0
      });
      // 5. 彻底重置3D文字及其相关对象的状态
      if (this.textGroup) {
        // 确保所有对文字的GSAP动画都已停止
        gsap.killTweensOf(this.textGroup.position);
        gsap.killTweensOf(this.textGroup.rotation);
        gsap.killTweensOf(this.textGroup.scale);

        // 恢复初始变换
        this.textGroup.position.set(0, 8, -18);
        this.textGroup.rotation.set(0.1, 0, 0);
        this.textGroup.scale.set(1, 1, 1);

        // 确保所有文字效果都可见
        this.textMeshes.forEach(m => m.visible = true);
        this.textOutlines.forEach(o => o.visible = true);
        this.textGlows.forEach(g => g.visible = true);
        this.textFlows.forEach(f => {
          f.visible = true;
          // 重置shader交互状态
          if (f.material.uniforms.isHovering !== undefined) {
            f.material.uniforms.isHovering.value = 0.0;
          }
          if (f.material.uniforms.mousePos) {
            f.material.uniforms.mousePos.value.set(10000, 10000, 10000);
          }
        });
      }
      // 6. 重置3D文字相关的光源位置和强度
      if (this.textSideLights.length > 0) {
        this.textSideLights.forEach(light => {
          if (light.userData.initialPosition) {
            light.position.copy(light.userData.initialPosition);
          }
          // 重置光源强度
          if (light.name === 'topLight') {
            light.intensity = 4;
          } else {
            light.intensity = 5;
          }
        });
      }
      // --- 修复结束 ---

      this.visible = true;
      this.startFadeOut = false;
      this.isFadingOut = false; // 重置退出状态
      this.showScrollUpHint = false; // 隐藏提示

      // 重置加载进度
      this.loadingProgress = 0;
      this.loadingComplete = false;

      // 初始化性能监控
      if (!this.stats) {
        this.stats = new Stats();
        this.stats.dom.style.position = 'absolute';
        this.stats.dom.style.right = '10px';
        this.stats.dom.style.bottom = '10px';
        this.stats.dom.style.left = 'auto';
        this.stats.dom.style.top = 'auto';
        this.stats.dom.style.zIndex = '10000';
        this.stats.dom.style.opacity = '0.7';
        this.stats.dom.style.transform = 'scale(0.8)';
        this.stats.dom.style.transformOrigin = 'bottom right';
        this.stats.dom.id = 'splash-fps-monitor';
        document.body.appendChild(this.stats.dom);
      }

      // 重启性能检测
      if (!this.fpsCheckInterval) {
        this.fpsCheckInterval = setInterval(() => this.checkPerformance(), 1000);
      }

      // 重新初始化动画
      this.clock = new THREE.Clock();
      this.lastFPSTime = performance.now();
      this.frameCount = 0;
      if (!this.animationFrame) { // 检查动画循环是否已停止
        this.animate(); // 重启动画循环
      }
      this.simulateLoading();

      // 重新触发GSAP动画
      this.$nextTick(() => {
        gsap.from('.glow-letter', {
          opacity: 0,
          y: 20,
          stagger: 0.05,
          duration: 1,
          ease: 'power2.out',
        });

        gsap.from('.keyword', {
          opacity: 0,
          scale: 0,
          stagger: 0.1,
          duration: 1,
          ease: 'elastic.out',
          delay: 1
        });
      });

      // 禁用页面滚动
      this.disableBodyScroll();

      // 重新绑定滚动事件
      window.addEventListener('wheel', this.scrollListener);
    },

    createStarField() {
      // 使用较小的星星数量优化性能
      const starCount = 1500;
      const starGeometry = new THREE.BufferGeometry();
      const starPositions = new Float32Array(starCount * 3);
      const starSizes = new Float32Array(starCount);
      const starColors = new Float32Array(starCount * 3);
      const starVelocities = new Float32Array(starCount * 3);

      for (let i = 0; i < starCount * 3; i += 3) {
        starPositions[i] = (Math.random() - 0.5) * 2000;
        starPositions[i + 1] = (Math.random() - 0.5) * 2000;
        starPositions[i + 2] = (Math.random() - 0.5) * 2000;

        starSizes[i / 3] = Math.random() * 2 + 0.5;

        const r = 0.3 + Math.random() * 0.4;
        const g = 0.5 + Math.random() * 0.3;
        const b = 0.6 + Math.random() * 0.2;

        starColors[i] = r;
        starColors[i + 1] = g;
        starColors[i + 2] = b;

        starVelocities[i] = (Math.random() - 0.5) * 0.1;
        starVelocities[i + 1] = (Math.random() - 0.5) * 0.05;
        starVelocities[i + 2] = (Math.random() - 0.5) * 0.1;
      }

      starGeometry.setAttribute('position', new THREE.BufferAttribute(starPositions, 3));
      starGeometry.setAttribute('size', new THREE.BufferAttribute(starSizes, 1));
      starGeometry.setAttribute('color', new THREE.BufferAttribute(starColors, 3));
      starGeometry.setAttribute('velocity', new THREE.BufferAttribute(starVelocities, 3));

      const starMaterial = new THREE.ShaderMaterial({
        uniforms: {
          time: { value: 0 },
          pixelRatio: { value: window.devicePixelRatio }
        },
        vertexShader: `
            uniform float time;
            uniform float pixelRatio;
            attribute float size;
            attribute vec3 color;
            attribute vec3 velocity;
            varying float vSize;
            varying vec3 vColor;
            varying float vDistanceEffect;
            
            void main() {
              vSize = size;
              vColor = color;
              
              float distToCenter = length(position) / 1000.0;
              vDistanceEffect = distToCenter;
              
              vec3 pos = position;
              float angle = time * 0.05;
              float x = pos.x * cos(angle) - pos.z * sin(angle);
              float z = pos.x * sin(angle) + pos.z * cos(angle);
              pos.x = x;
              pos.z = z;
              
              pos.y += sin(time * 0.2 + pos.x * 0.01) * 5.0;
              
              pos += velocity * time * 10.0;
              
              float limit = 1000.0;
              if(pos.x > limit) pos.x -= 2.0 * limit;
              if(pos.x < -limit) pos.x += 2.0 * limit;
              if(pos.y > limit) pos.y -= 2.0 * limit;
              if(pos.y < -limit) pos.y += 2.0 * limit;
              if(pos.z > limit) pos.z -= 2.0 * limit;
              if(pos.z < -limit) pos.z += 2.0 * limit;
              
              vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
              gl_PointSize = size * pixelRatio * (300.0 / -mvPosition.z);
              gl_Position = projectionMatrix * mvPosition;
            }
          `,
        fragmentShader: `
            varying float vSize;
            varying vec3 vColor;
            varying float vDistanceEffect;
            
            void main() {
              float dist = length(gl_PointCoord - vec2(0.5));
              if (dist > 0.5) discard;
              
              float intensity = pow(1.0 - dist * 2.0, 1.5);
              
              float brightnessFactor = mix(0.3, 1.0, vDistanceEffect);
              
              gl_FragColor = vec4(vColor * brightnessFactor, intensity * 0.8);
            }
          `,
        transparent: true,
        blending: THREE.AdditiveBlending,
        depthWrite: false
      });

      const stars = new THREE.Points(starGeometry, starMaterial);
      stars.userData.autoAnimate = true;
      this.scene.add(stars);
      this.stars = stars;
    },
    createGalaxy() {
      const galaxyGeometry = new THREE.BufferGeometry();
      const galaxyCount = 9000; // 减少银河粒子数量
      const positions = new Float32Array(galaxyCount * 3);
      const colors = new Float32Array(galaxyCount * 3);
      const sizes = new Float32Array(galaxyCount);
      const rotationSpeeds = new Float32Array(galaxyCount);

      const colorInside = new THREE.Color(0x0a1a33);
      const colorOutside = new THREE.Color(0x4a0072);

      const radius = 30;
      const branches = 5;
      const spin = 1;
      const randomness = 0.5;
      const randomnessPower = 3;

      for (let i = 0; i < galaxyCount; i++) {
        const i3 = i * 3;

        const radius_i = Math.random() * radius;
        const branchAngle = (i % branches) / branches * Math.PI * 2;
        const spinAngle = radius_i * spin;

        const randomX = Math.pow(Math.random(), randomnessPower) * (Math.random() < 0.5 ? 1 : -1) * randomness * radius_i;
        const randomY = Math.pow(Math.random(), randomnessPower) * (Math.random() < 0.5 ? 1 : -1) * randomness * radius_i;
        const randomZ = Math.pow(Math.random(), randomnessPower) * (Math.random() < 0.5 ? 1 : -1) * randomness * radius_i;

        positions[i3] = Math.cos(branchAngle + spinAngle) * radius_i + randomX;
        positions[i3 + 1] = randomY;
        positions[i3 + 2] = Math.sin(branchAngle + spinAngle) * radius_i + randomZ;

        const mixedColor = colorInside.clone();
        mixedColor.lerp(colorOutside, radius_i / radius);

        colors[i3] = mixedColor.r;
        colors[i3 + 1] = mixedColor.g;
        colors[i3 + 2] = mixedColor.b;

        sizes[i] = Math.random() * 1.5 + 0.3;

        rotationSpeeds[i] = 0.5 + radius_i / radius * 2.0;
      }

      galaxyGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
      galaxyGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
      galaxyGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
      galaxyGeometry.setAttribute('rotationSpeed', new THREE.BufferAttribute(rotationSpeeds, 1));

      const galaxyMaterial = new THREE.ShaderMaterial({
        uniforms: {
          time: { value: 0 },
          pixelRatio: { value: window.devicePixelRatio }
        },
        vertexShader: `
            uniform float time;
            uniform float pixelRatio;
            attribute vec3 color;
            attribute float size;
            attribute float rotationSpeed;
            varying vec3 vColor;
            varying float vDistance;
            
            void main() {
              vColor = color;
              
              vDistance = length(position) / 30.0;
              
              vec3 pos = position;
              float angle = time * 0.1 * rotationSpeed;
              float x = pos.x * cos(angle) - pos.z * sin(angle);
              float z = pos.x * sin(angle) + pos.z * cos(angle);
              pos.x = x;
              pos.z = z;
              
              pos.y += sin(time * 0.2 + position.x * 0.05) * 0.3;
              
              vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
              gl_PointSize = size * pixelRatio * (300.0 / -mvPosition.z);
              gl_Position = projectionMatrix * mvPosition;
            }
          `,
        fragmentShader: `
            varying vec3 vColor;
            varying float vDistance;
            void main() {
              float dist = length(gl_PointCoord - vec2(0.5));
              if (dist > 0.5) discard;
              
              float intensity = 1.0 - dist * 2.0;
              float alpha = intensity * mix(0.2, 0.7, vDistance);
              
              gl_FragColor = vec4(vColor, alpha);
            }
          `,
        transparent: true,
        blending: THREE.AdditiveBlending,
        depthWrite: false
      });

      this.galaxyParticles = new THREE.Points(galaxyGeometry, galaxyMaterial);
      this.galaxyParticles.position.set(-20, -10, -10);
      this.scene.add(this.galaxyParticles);
    },
    createParticles() {
      // 准备粒子纹理
      const textureLoader = new THREE.TextureLoader();
      // 使用具有开放CORS策略的图像URL来避免跨域错误
      this.particleTexture = textureLoader.load('https://raw.githubusercontent.com/mrdoob/three.js/master/examples/textures/sprites/disc.png'); // 柔和的粒子纹理

      // 粒子几何体
      const geometry = new THREE.BufferGeometry();

      // 优化粒子数量，保持视觉效果但提高性能
      const particleCount = 15000; // 减少40%的粒子数量

      // 准备数组
      const positions = new Float32Array(particleCount * 3);
      const colors = new Float32Array(particleCount * 3);
      const sizes = new Float32Array(particleCount);
      const opacities = new Float32Array(particleCount);
      const velocities = new Float32Array(particleCount * 3);
      const turbulence = new Float32Array(particleCount); // 添加湍流参数

      // 粒子分布范围
      const range = 1000;

      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;

        // 随机位置
        positions[i3] = (Math.random() - 0.5) * range;
        positions[i3 + 1] = (Math.random() - 0.5) * range;
        positions[i3 + 2] = (Math.random() - 0.5) * range;

        // 计算到中心的距离
        const distance = Math.sqrt(
          positions[i3] * positions[i3] +
          positions[i3 + 1] * positions[i3 + 1] +
          positions[i3 + 2] * positions[i3 + 2]
        );
        const normalizedDist = Math.min(distance / (range * 0.5), 1.0);

        // 根据距离设置颜色 - 从中心到外围的渐变
        const insideColor = new THREE.Color(0x1a237e); // 深蓝色
        const outsideColor = new THREE.Color(0x7b1fa2); // 紫色
        const color = new THREE.Color().lerpColors(insideColor, outsideColor, normalizedDist);

        colors[i3] = color.r;
        colors[i3 + 1] = color.g;
        colors[i3 + 2] = color.b;

        // 根据距离设置大小 - 中心区域更小的粒子（减少光强）
        const size = Math.max(0.8, 2.5 * (1 - Math.pow(1 - normalizedDist, 2)));
        sizes[i] = size;

        // 根据距离设置不透明度 - 中心区域极低不透明度
        // 进一步降低中心区域的不透明度，增强外围区域
        const opacity = normalizedDist < 0.2 ?
          0.05 + normalizedDist * 0.25 : // 中心区域非常低的不透明度
          0.1 + normalizedDist * 0.6;    // 外围区域较高不透明度
        opacities[i] = opacity;

        // 添加自动流动的速度向量 - 更合理的流动
        const phi = Math.random() * Math.PI * 2; // 水平角度
        const theta = Math.random() * Math.PI;   // 垂直角度
        const speed = 0.1 + Math.random() * 0.3; // 速度大小

        // 球坐标到笛卡尔坐标的转换，创建均匀分布的速度方向
        velocities[i3] = Math.sin(theta) * Math.cos(phi) * speed;
        velocities[i3 + 1] = Math.sin(theta) * Math.sin(phi) * speed;
        velocities[i3 + 2] = Math.cos(theta) * speed;

        // 添加湍流参数 - 使粒子运动更自然
        turbulence[i] = Math.random();
      }

      // 设置粒子属性
      geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
      geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
      geometry.setAttribute('size', new THREE.Float32BufferAttribute(sizes, 1));
      geometry.setAttribute('opacity', new THREE.Float32BufferAttribute(opacities, 1));
      geometry.setAttribute('velocity', new THREE.Float32BufferAttribute(velocities, 3));
      geometry.setAttribute('turbulence', new THREE.Float32BufferAttribute(turbulence, 1));

      // 粒子着色器材质
      const material = new THREE.ShaderMaterial({
        uniforms: {
          time: { value: 0 },
          pixelRatio: { value: window.devicePixelRatio },
          pointTexture: { value: this.particleTexture }
        },
        vertexShader: `
          attribute vec3 color;
          attribute float size;
          attribute float opacity;
          attribute vec3 velocity;
          attribute float turbulence;
          
          uniform float time;
          uniform float pixelRatio;
          
          varying vec3 vColor;
          varying float vOpacity;
          
          // 噪声函数，创造更自然的运动
          vec3 mod289(vec3 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
          vec4 mod289(vec4 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
          vec4 permute(vec4 x) { return mod289(((x*34.0)+1.0)*x); }
          vec4 taylorInvSqrt(vec4 r) { return 1.79284291400159 - 0.85373472095314 * r; }
          
          float snoise(vec3 v) {
            const vec2 C = vec2(1.0/6.0, 1.0/3.0);
            const vec4 D = vec4(0.0, 0.5, 1.0, 2.0);
            
            // 计算格子的索引
            vec3 i  = floor(v + dot(v, C.yyy));
            vec3 x0 = v - i + dot(i, C.xxx);
            
            // 计算相对于格子顶点的位置
            vec3 g = step(x0.yzx, x0.xyz);
            vec3 l = 1.0 - g;
            vec3 i1 = min(g.xyz, l.zxy);
            vec3 i2 = max(g.xyz, l.zxy);
            
            vec3 x1 = x0 - i1 + C.xxx;
            vec3 x2 = x0 - i2 + C.yyy;
            vec3 x3 = x0 - D.yyy;
            
            // 生成随机梯度
            i = mod289(i);
            vec4 p = permute(permute(permute(
                     i.z + vec4(0.0, i1.z, i2.z, 1.0))
                     + i.y + vec4(0.0, i1.y, i2.y, 1.0))
                     + i.x + vec4(0.0, i1.x, i2.x, 1.0));
                     
            // 梯度衰减
            float n_ = 0.142857142857;
            vec3 ns = n_ * D.wyz - D.xzx;
            
            vec4 j = p - 49.0 * floor(p * ns.z * ns.z);
            
            vec4 x_ = floor(j * ns.z);
            vec4 y_ = floor(j - 7.0 * x_);
            
            vec4 x = x_ * ns.x + ns.yyyy;
            vec4 y = y_ * ns.x + ns.yyyy;
            vec4 h = 1.0 - abs(x) - abs(y);
            
            vec4 b0 = vec4(x.xy, y.xy);
            vec4 b1 = vec4(x.zw, y.zw);
            
            vec4 s0 = floor(b0) * 2.0 + 1.0;
            vec4 s1 = floor(b1) * 2.0 + 1.0;
            vec4 sh = -step(h, vec4(0.0));
            
            vec4 a0 = b0.xzyw + s0.xzyw * sh.xxyy;
            vec4 a1 = b1.xzyw + s1.xzyw * sh.zzww;
            
            vec3 p0 = vec3(a0.xy, h.x);
            vec3 p1 = vec3(a0.zw, h.y);
            vec3 p2 = vec3(a1.xy, h.z);
            vec3 p3 = vec3(a1.zw, h.w);
            
            // 归一化梯度
            vec4 norm = taylorInvSqrt(vec4(dot(p0, p0), dot(p1, p1), dot(p2, p2), dot(p3, p3)));
            p0 *= norm.x;
            p1 *= norm.y;
            p2 *= norm.z;
            p3 *= norm.w;
            
            // 混合结果
            vec4 m = max(0.6 - vec4(dot(x0, x0), dot(x1, x1), dot(x2, x2), dot(x3, x3)), 0.0);
            m = m * m;
            return 42.0 * dot(m * m, vec4(dot(p0, x0), dot(p1, x1), dot(p2, x2), dot(p3, x3)));
          }
          
          void main() {
            vColor = color;
            vOpacity = opacity;
            
            // 基于时间的湍流运动参数
            float noiseTime = time * 0.5;
            float turbScale = turbulence * 2.0;
            
            // 为每个粒子创建独特的噪声偏移
            vec3 noisePos = position * 0.02 + velocity * noiseTime;
            
            // 用噪声函数创建自然流动
            float noise1 = snoise(noisePos) * turbScale;
            float noise2 = snoise(noisePos + vec3(100.0, 100.0, 100.0)) * turbScale;
            float noise3 = snoise(noisePos + vec3(-100.0, -100.0, -100.0)) * turbScale;
            
            // 合并噪声和线性速度
            vec3 movement = velocity * time * 5.0;
            vec3 turbulentForce = vec3(noise1, noise2, noise3) * 5.0;
            
            // 最终位置计算，包括线性运动和湍流
            vec3 movingPosition = position + movement + turbulentForce;
            
            // 边界处理，使粒子在范围内循环
            float range = 500.0;
            vec3 wrappedPosition = mod(movingPosition + range, range * 2.0) - range;
            
            // 应用模型视图矩阵，计算屏幕位置和大小
            vec4 mvPosition = modelViewMatrix * vec4(wrappedPosition, 1.0);
            gl_PointSize = size * pixelRatio * (300.0 / -mvPosition.z);
            gl_Position = projectionMatrix * mvPosition;
          }
        `,
        fragmentShader: `
          uniform sampler2D pointTexture;
          varying vec3 vColor;
          varying float vOpacity;
          
          void main() {
            // 使用粒子纹理，改进发光效果
            vec4 texColor = texture2D(pointTexture, gl_PointCoord);
            
            // 创建柔和的圆形粒子，光滑的边缘
            float distFromCenter = length(gl_PointCoord - vec2(0.5));
            float alpha = smoothstep(0.5, 0.1, distFromCenter) * vOpacity;
            
            // 最终颜色
            gl_FragColor = vec4(vColor, alpha) * texColor;
          }
        `,
        blending: THREE.AdditiveBlending,
        depthTest: false,
        transparent: true
      });

      // 创建粒子系统
      this.particles = new THREE.Points(geometry, material);
      this.scene.add(this.particles);
    },
    createLines() {
      const lineCount = 50;
      const geometry = new THREE.BufferGeometry();
      const positions = new Float32Array(lineCount * 6);
      const lineMaterial = new THREE.LineBasicMaterial({
        color: 0x64b5f6,
        transparent: true,
        opacity: 0.5,
        blending: THREE.AdditiveBlending
      });

      for (let i = 0; i < lineCount * 6; i += 6) {
        const p1 = Math.floor(Math.random() * 1000);
        const p2 = Math.floor(Math.random() * 1000);
        const pos = this.particles.geometry.attributes.position.array;
        positions[i] = pos[p1 * 3];
        positions[i + 1] = pos[p1 * 3 + 1];
        positions[i + 2] = pos[p1 * 3 + 2];
        positions[i + 3] = pos[p2 * 3];
        positions[i + 4] = pos[p2 * 3 + 1];
        positions[i + 5] = pos[p2 * 3 + 2];
      }

      geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
      this.lines = new THREE.LineSegments(geometry, lineMaterial);
      this.scene.add(this.lines);
    },
    createGrid() {
      // 创建网格地面
      const gridSize = 100;
      const gridDivisions = 50;
      const gridMaterial = new THREE.ShaderMaterial({
        uniforms: {
          time: { value: 0 },
          color1: { value: new THREE.Color(0x0d47a1) },
          color2: { value: new THREE.Color(0x64b5f6) }
        },
        vertexShader: `
            varying vec3 vPosition;
            void main() {
              vPosition = position;
              gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
          `,
        fragmentShader: `
            uniform float time;
            uniform vec3 color1;
            uniform vec3 color2;
            varying vec3 vPosition;
            
            void main() {
              float grid = 0.0;
              
              // 主网格线
              float mainGrid1 = abs(fract(vPosition.x / 10.0 - 0.5) - 0.5) / fwidth(vPosition.x) * 2.0;
              float mainGrid2 = abs(fract(vPosition.z / 10.0 - 0.5) - 0.5) / fwidth(vPosition.z) * 2.0;
              mainGrid1 = 1.0 - clamp(mainGrid1, 0.0, 1.0);
              mainGrid2 = 1.0 - clamp(mainGrid2, 0.0, 1.0);
              float mainGrid = max(mainGrid1, mainGrid2);
              
              // 次网格线
              float subGrid1 = abs(fract(vPosition.x - 0.5) - 0.5) / fwidth(vPosition.x) * 0.5;
              float subGrid2 = abs(fract(vPosition.z - 0.5) - 0.5) / fwidth(vPosition.z) * 0.5;
              subGrid1 = 1.0 - clamp(subGrid1, 0.0, 1.0);
              subGrid2 = 1.0 - clamp(subGrid2, 0.0, 1.0);
              float subGrid = max(subGrid1, subGrid2) * 0.5;
              
              grid = max(mainGrid, subGrid);
              
              // 距离衰减
              float dist = length(vPosition.xz);
              float fade = 1.0 - smoothstep(30.0, 100.0, dist);
              
              // 脉冲效果
              float pulse = 0.5 + 0.5 * sin(time * 0.5 - dist * 0.1);
              
              // 最终颜色
              vec3 color = mix(color1, color2, pulse);
              gl_FragColor = vec4(color, grid * fade * 0.8);
            }
          `,
        transparent: true,
        blending: THREE.AdditiveBlending,
        depthWrite: false,
        side: THREE.DoubleSide
      });

      const gridGeometry = new THREE.PlaneGeometry(gridSize, gridSize, gridDivisions, gridDivisions);
      gridGeometry.rotateX(-Math.PI / 2);
      gridGeometry.translate(0, -15, 0);

      const grid = new THREE.Mesh(gridGeometry, gridMaterial);
      this.scene.add(grid);
    },
    createHolographicSphere() {
      // 创建全息球体
      const sphereGeometry = new THREE.IcosahedronGeometry(8, 2); // 降低复杂度
      const sphereMaterial = new THREE.ShaderMaterial({
        uniforms: {
          time: { value: 0 },
          color1: { value: new THREE.Color(0x2196f3) },
          color2: { value: new THREE.Color(0x9c27b0) }
        },
        vertexShader: `
            uniform float time;
            varying vec3 vNormal;
            varying vec3 vPosition;
            
            void main() {
              vNormal = normal;
              vPosition = position;
              
              // 添加波动效果
              float displacement = sin(position.x * 5.0 + time) * sin(position.y * 5.0 + time) * sin(position.z * 5.0 + time) * 0.2;
              vec3 newPosition = position + normal * displacement;
              
              gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0);
            }
          `,
        fragmentShader: `
            uniform float time;
            uniform vec3 color1;
            uniform vec3 color2;
            varying vec3 vNormal;
            varying vec3 vPosition;
            
            void main() {
              // 全息网格效果
              float grid1 = abs(fract(vPosition.x * 5.0 + time * 0.1) - 0.5);
              float grid2 = abs(fract(vPosition.y * 5.0 + time * 0.1) - 0.5);
              float grid3 = abs(fract(vPosition.z * 5.0 + time * 0.1) - 0.5);
              
              float grid = min(min(grid1, grid2), grid3);
              grid = smoothstep(0.45, 0.5, grid);
              
              // 边缘发光效果
              float rimLight = 1.0 - max(0.0, dot(normalize(vNormal), normalize(vec3(0.0, 0.0, 1.0))));
              rimLight = pow(rimLight, 2.0);
              
              // 颜色混合
              vec3 color = mix(color1, color2, 0.5 + 0.5 * sin(time * 0.5 + vPosition.x + vPosition.y + vPosition.z));
              
              // 最终颜色
              float alpha = mix(0.1, 0.8, grid + rimLight);
              gl_FragColor = vec4(color, alpha);
            }
          `,
        transparent: true,
        blending: THREE.AdditiveBlending,
        side: THREE.DoubleSide,
        depthWrite: false
      });

      const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
      sphere.position.set(15, 0, -10);
      this.scene.add(sphere);
      this.interactiveObjects.push(sphere);

      // 添加光环
      const ringGeometry = new THREE.RingGeometry(8.5, 10, 64);
      const ringMaterial = new THREE.ShaderMaterial({
        uniforms: {
          time: { value: 0 }
        },
        vertexShader: `
            uniform float time;
            varying vec2 vUv;
            
            void main() {
              vUv = uv;
              gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
          `,
        fragmentShader: `
            uniform float time;
            varying vec2 vUv;
            
            void main() {
              float angle = atan(vUv.y - 0.5, vUv.x - 0.5);
              float dist = length(vUv - vec2(0.5));
              float alpha = smoothstep(0.48, 0.5, dist) * smoothstep(0.52, 0.5, dist);
              alpha *= 0.5 + 0.5 * sin(angle * 20.0 + time * 2.0);
              
              vec3 color = vec3(0.3, 0.7, 1.0);
              gl_FragColor = vec4(color, alpha);
            }
          `,
        transparent: true,
        blending: THREE.AdditiveBlending,
        side: THREE.DoubleSide,
        depthWrite: false
      });

      const ring = new THREE.Mesh(ringGeometry, ringMaterial);
      ring.position.copy(sphere.position);
      ring.rotation.x = Math.PI / 2;
      this.scene.add(ring);
    },
    cleanup3DText() {
      if (!this.textGroup) return;

      // 从可交互对象数组中移除旧的文本网格
      this.interactiveObjects = this.interactiveObjects.filter(
        obj => !this.textMeshes.includes(obj)
      );

      this.scene.remove(this.textGroup);

      // 递归地释放组中的所有对象
      this.textGroup.traverse(object => {
        if (object.geometry) object.geometry.dispose();
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose());
          } else {
            object.material.dispose();
          }
        }
      });

      // 重置所有与文本相关的数组
      this.textGroup = null;
      this.textMeshes = [];
      this.textGlows = [];
      this.textFlows = [];
      this.textOutlines = [];
      this.textSideLights = [];
    },
    create3DText() {
      const createTextObjects = (font) => {
        this.cleanup3DText();

        this.textGroup = new THREE.Group();
        this.textGroup.position.set(0, 8, -18);
        this.textGroup.rotation.x = 0.1;
        this.scene.add(this.textGroup);

        const isMobile = window.innerWidth <= 768;
        const texts = isMobile ? ['KINGCOLA', 'ICG'] : ['KINGCOLA ICG'];
        const yOffsets = isMobile ? [1.5, -1.5] : [0]; // 进一步调整移动端行间距
        const size = isMobile ? 1.8 : 4.5; // 进一步减小移动端字体大小

        if (isMobile) {
          this.textGroup.position.y = 7; // 调整移动端Y轴位置
        }

        texts.forEach((text, index) => {
          const textGeometry = new TextGeometry(text, {
            font: font, size: size, height: 1.4, curveSegments: 12,
            bevelEnabled: true, bevelThickness: 0.4, bevelSize: 0.25,
            bevelOffset: 0, bevelSegments: 5
          });
          textGeometry.center();

          const materials = [
            new THREE.MeshStandardMaterial({
              color: 0x4fc3f7, metalness: 0.9, roughness: 0.1,
              emissive: 0x1565c0, emissiveIntensity: 0.5
            }),
            new THREE.MeshStandardMaterial({
              color: 0xb3e5fc, metalness: 1.0, roughness: 0.05,
              emissive: 0x40c4ff, emissiveIntensity: 0.6
            })
          ];

          const textMesh = new THREE.Mesh(textGeometry, materials);
          textMesh.position.y = yOffsets[index];
          textMesh.castShadow = true;
          textMesh.renderOrder = 10;

          this.textGroup.add(textMesh);
          this.textMeshes.push(textMesh);
          this.interactiveObjects.push(textMesh);

          const outlineMaterial = new THREE.MeshBasicMaterial({ color: 0x0d47a1, side: THREE.BackSide });
          const outlineMesh = new THREE.Mesh(textGeometry, outlineMaterial);
          outlineMesh.position.copy(textMesh.position);
          outlineMesh.scale.multiplyScalar(1.05);
          this.textGroup.add(outlineMesh);
          this.textOutlines.push(outlineMesh);

          const glowMaterial = new THREE.ShaderMaterial({
            uniforms: {
              time: { value: 0 }, color: { value: new THREE.Color(0x4fc3f7) }, alpha: { value: 0.4 }
            },
            vertexShader: `
              uniform float time; varying vec3 vPosition; varying vec3 vNormal;
              void main() {
                vPosition = position; vNormal = normal;
                float pulse = 0.03 * sin(time * 2.0);
                vec3 newPosition = position + normal * pulse;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0);
              }
            `,
            fragmentShader: `
              uniform float time; uniform vec3 color; uniform float alpha; varying vec3 vPosition; varying vec3 vNormal;
              void main() {
                float rim = pow(1.0 - abs(dot(normalize(vNormal), normalize(vec3(0, 0, 1)))), 2.0);
                float glow = rim * (0.6 + 0.2 * sin(time * 3.0 + vPosition.x * 0.5));
                vec3 glowColor = mix(color, vec3(0.8, 0.9, 1.0), glow * 0.4);
                gl_FragColor = vec4(glowColor, alpha * glow);
              }
            `,
            transparent: true, blending: THREE.AdditiveBlending, side: THREE.FrontSide, depthWrite: false
          });
          const glowMesh = new THREE.Mesh(textGeometry, glowMaterial);
          glowMesh.position.copy(textMesh.position);
          glowMesh.scale.multiplyScalar(1.03);
          this.textGroup.add(glowMesh);
          this.textGlows.push(glowMesh);

          const flowMaterial = new THREE.ShaderMaterial({
            uniforms: {
              time: { value: 0 }, baseColor: { value: new THREE.Color(0x4fc3f7) },
              flowColor: { value: new THREE.Color(0xffffff) },
              mousePos: { value: new THREE.Vector3(10000, 10000, 10000) }, isHovering: { value: 0.0 }
            },
            vertexShader: `
              uniform float time; varying vec3 vPosition; varying vec2 vUv; varying vec3 vNormal;
              void main() {
                vPosition = position; vUv = uv; vNormal = normal;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
              }
            `,
            fragmentShader: `
              uniform float time; uniform vec3 baseColor; uniform vec3 flowColor; uniform vec3 mousePos; uniform float isHovering;
              varying vec3 vPosition; varying vec2 vUv; varying vec3 vNormal;
              void main() {
                float grid = max(smoothstep(0.9, 0.95, fract(vUv.x * 25.0)), smoothstep(0.9, 0.95, fract(vUv.y * 25.0)));
                float pulse1 = fract(vUv.x * 10.0 - time * 2.0);
                pulse1 = pow(smoothstep(0.0, 0.05, pulse1) * smoothstep(0.1, 0.05, pulse1), 2.0);
                float pulse2 = fract(vUv.y * 15.0 + time * 3.0);
                pulse2 = pow(smoothstep(0.0, 0.05, pulse2) * smoothstep(0.1, 0.05, pulse2), 2.0);
                float pulses = (pulse1 + pulse2) * grid;
                float scanline = fract(vUv.y + time * -0.4);
                scanline = pow(smoothstep(0.0, 0.015, scanline) * smoothstep(0.03, 0.015, scanline), 4.0) * 2.5;
                float rim = pow(1.0 - abs(dot(normalize(vNormal), normalize(vec3(0.0, 0.0, 1.0)))), 3.0);
                rim = smoothstep(0.0, 1.0, rim) * 0.8;
                float interactiveGlow = 0.0;
                if (isHovering > 0.5) {
                  float dist = distance(vPosition, mousePos);
                  interactiveGlow = pow(smoothstep(8.0, 0.0, dist), 2.0) * 2.0;
                }
                vec3 color = baseColor * (0.3 + grid * 0.4);
                color = mix(color, flowColor, pulses * 4.0);
                color = mix(color, vec3(0.8, 0.9, 1.0), scanline);
                color += flowColor * rim;
                color += flowColor * interactiveGlow;
                float alpha = clamp(grid * 0.3 + pulses * 1.5 + scanline * 0.8 + rim * 0.7 + interactiveGlow, 0.0, 1.0);
                gl_FragColor = vec4(color, alpha);
              }
            `,
            transparent: true, blending: THREE.AdditiveBlending, side: THREE.FrontSide, depthWrite: false
          });
          const flowMesh = new THREE.Mesh(textGeometry, flowMaterial);
          flowMesh.position.copy(textMesh.position);
          flowMesh.scale.multiplyScalar(1.01);
          this.textGroup.add(flowMesh);
          this.textFlows.push(flowMesh);
        });

        const leftLight = new THREE.PointLight(0x40c4ff, 5, 35);
        leftLight.name = 'sideLight';
        leftLight.position.set(-18, 2, 10);
        leftLight.userData.initialPosition = leftLight.position.clone();
        this.textGroup.add(leftLight);

        const rightLight = new THREE.PointLight(0x40c4ff, 5, 35);
        rightLight.name = 'sideLight';
        rightLight.position.set(18, 2, 10);
        rightLight.userData.initialPosition = rightLight.position.clone();
        this.textGroup.add(rightLight);

        const topLight = new THREE.PointLight(0x40c4ff, 4, 30);
        topLight.name = 'topLight';
        topLight.position.set(0, 12, 10);
        topLight.userData.initialPosition = topLight.position.clone();
        this.textGroup.add(topLight);

        this.textSideLights = [leftLight, rightLight, topLight];
      }

      if (this.font) {
        createTextObjects(this.font);
      } else {
        const loader = new FontLoader();
        loader.load('https://threejs.org/examples/fonts/helvetiker_bold.typeface.json', (font) => {
          this.font = font;
          createTextObjects(font);
        });
      }
    },
    setupPostProcessing() {
      // 设置后期处理效果
      this.composer = new EffectComposer(this.renderer);

      const renderPass = new RenderPass(this.scene, this.camera);
      this.composer.addPass(renderPass);

      // 优化后处理效果，降低分辨率和复杂度
      const bloomPass = new UnrealBloomPass(
        new THREE.Vector2(window.innerWidth * 0.7, window.innerHeight * 0.7), // 降低分辨率
        0.3,    // 保持强度不变
        0.3,   // 略微减小半径
        1.0    // 提高阈值，减少计算量
      );
      this.composer.addPass(bloomPass);

      // 添加自定义着色器通道，处理高光溢出问题
      const combinedShader = {
        uniforms: {
          tDiffuse: { value: null },
          time: { value: 0 }
        },
        vertexShader: `
          varying vec2 vUv;
          void main() {
            vUv = uv;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
          }
        `,
        fragmentShader: `
          uniform sampler2D tDiffuse;
          uniform float time;
          varying vec2 vUv;
          
          void main() {
            vec2 uv = vUv;
            
            // 1. 色差效果
            float r = texture2D(tDiffuse, uv + vec2(0.001 * sin(time), 0.0)).r;
            float g = texture2D(tDiffuse, uv).g;
            float b = texture2D(tDiffuse, uv - vec2(0.001 * sin(time), 0.0)).b;
            vec3 color = vec3(r, g, b);
            
            // 2. 扫描线效果
            float scanline = sin(uv.y * 800.0) * 0.01;
            color += scanline;

            // 3. 暗角效果
            float vignette = 1.0 - smoothstep(0.3, 1.1, length(uv - 0.5) * 2.0);
            vignette = pow(vignette, 1.8);
            
            // 4. 亮度计算和高光压制 (合并逻辑)
            float luminance = dot(color, vec3(0.299, 0.587, 0.114));
            
            // a. 整体高光压制
            float threshold = 0.7;
            if (luminance > threshold) {
              float reduction = 1.0 - pow((luminance - threshold) / (1.0 - threshold), 0.5);
              color *= mix(0.6, 1.0, reduction);
            }
            
            // b. 中心区域高光压制
            float distFromCenter = length(uv - vec2(0.5));
            float centerEffect = 1.0 - smoothstep(0.0, 0.4, distFromCenter);
            if (centerEffect > 0.1 && luminance > 0.6) {
                float centerReduction = mix(1.0, 0.5, centerEffect);
                color *= centerReduction;
            }
            
            // 5. 最终调整
            color *= 0.85;
            color *= vignette * 0.9 + 0.1;
            
            gl_FragColor = vec4(color, 1.0);
          }
        `
      };

      const combinedPass = new ShaderPass(combinedShader);
      combinedPass.renderToScreen = true;
      this.composer.addPass(combinedPass);
    },
    animate() {
      this.animationFrame = requestAnimationFrame(this.animate);

      // 更新性能监控
      if (this.stats) {
        this.stats.update();
      }

      // 更新时间
      const time = performance.now() * 0.001; // 秒为单位

      // 更新着色器通道的时间
      if (this.composer && this.composer.passes.length > 1) {
        for (let i = 0; i < this.composer.passes.length; i++) {
          const pass = this.composer.passes[i];
          if (pass.uniforms && pass.uniforms.time) {
            pass.uniforms.time.value = time;
          }
        }
      }

      // 更新粒子系统的时间
      if (this.particles && this.particles.material.uniforms) {
        this.particles.material.uniforms.time.value = time;
      }

      // 更新银河系粒子的时间
      if (this.galaxyParticles && this.galaxyParticles.material.uniforms) {
        this.galaxyParticles.material.uniforms.time.value = time;
      }

      // 更新3D文字的时间
      if (this.textGlows.length > 0) {
        this.textGlows.forEach(glow => {
          if (glow.material.uniforms) {
            glow.material.uniforms.time.value = time;
          }
        });
      }
      if (this.textFlows.length > 0) {
        this.textFlows.forEach(flow => {
          if (flow.material.uniforms) {
            flow.material.uniforms.time.value = time;
          }
        });
      }

      // 更新流动边缘效果
      if (this.flowingEdges) {
        this.flowingEdges.children.forEach(child => {
          if (child.material.uniforms && child.material.uniforms.time) {
            child.material.uniforms.time.value = time;
          }
        });
      }

      // LERP平滑鼠标移动
      this.smoothedMouse.lerp(this.mouse, 0.05);

      // 自动呼吸旋转
      const autoRotateX = Math.sin(time * 0.1) * 0.02;
      const autoRotateY = Math.cos(time * 0.08) * 0.02;

      // 结合自动旋转和平滑后的鼠标影响
      const targetRotX = autoRotateX + this.smoothedMouse.y * 0.15;
      const targetRotY = autoRotateY + this.smoothedMouse.x * 0.15;

      // 平滑地将场景元素旋转到目标角度
      if (this.particles) {
        this.particles.rotation.x += (targetRotX - this.particles.rotation.x) * 0.05;
        this.particles.rotation.y += (targetRotY - this.particles.rotation.y) * 0.05;
        this.particles.rotation.z += 0.0001; // 缓慢自转
      }

      if (this.textGroup) {
        const textTargetRotX = 0.1 + this.smoothedMouse.y * 0.08;
        const textTargetRotY = this.smoothedMouse.x * 0.08;
        this.textGroup.rotation.x += (textTargetRotX - this.textGroup.rotation.x) * 0.05;
        this.textGroup.rotation.y += (textTargetRotY - this.textGroup.rotation.y) * 0.05;

        // 文字呼吸效果应用于整个组
        const breathScale = 1 + Math.sin(time * 0.8) * 0.005;
        this.textGroup.scale.set(breathScale, breathScale, breathScale);
      }

      if (this.galaxyParticles) {
        const galaxyTargetRotX = this.smoothedMouse.y * 0.2;
        const galaxyTargetRotY = this.smoothedMouse.x * 0.2;
        this.galaxyParticles.rotation.x += (galaxyTargetRotX - this.galaxyParticles.rotation.x) * 0.05;
        this.galaxyParticles.rotation.y += (galaxyTargetRotY - this.galaxyParticles.rotation.y) * 0.05;
        this.galaxyParticles.rotation.z += 0.0005;
      }

      // 如果有环，也添加动画
      if (this.ring) {
        this.ring.rotation.z = time * 0.1;
        this.ring.rotation.x = Math.sin(time * 0.2) * 0.1;
      }

      // 星场旋转，增加背景动态感
      if (this.stars) {
        this.stars.rotation.y = time * 0.03;
        this.stars.rotation.x = Math.sin(time * 0.05) * 0.02;
      }

      // 优化线条更新频率，减少计算量
      // 线条更新 - 定期重连线条端点，创造有机的连接变化
      if (this.lines && this.particles) {
        // 减少更新频率，从30帧更新一次改为60帧，降低50%的计算频率
        if (Math.floor(time * 10) % 60 === 0) {
          const linePositions = this.lines.geometry.attributes.position.array;
          const particlePositions = this.particles.geometry.attributes.position.array;
          const particleCount = particlePositions.length / 3;

          // 每次只更新5条线，而不是10条
          for (let i = 0; i < 5; i++) {
            const lineIndex = Math.floor(Math.random() * (linePositions.length / 6)) * 6;
            const p1 = Math.floor(Math.random() * particleCount) * 3;
            const p2 = Math.floor(Math.random() * particleCount) * 3;

            linePositions[lineIndex] = particlePositions[p1];
            linePositions[lineIndex + 1] = particlePositions[p1 + 1];
            linePositions[lineIndex + 2] = particlePositions[p1 + 2];
            linePositions[lineIndex + 3] = particlePositions[p2];
            linePositions[lineIndex + 4] = particlePositions[p2 + 1];
            linePositions[lineIndex + 5] = particlePositions[p2 + 2];
          }

          this.lines.geometry.attributes.position.needsUpdate = true;
        }
      }

      // 更新数据立方体动画
      if (this.dataCubes) {
        this.dataCubes.children.forEach(cube => {
          // 旋转动画
          cube.rotation.x += cube.userData.rotationSpeed.x;
          cube.rotation.y += cube.userData.rotationSpeed.y;
          cube.rotation.z += cube.userData.rotationSpeed.z;

          // 浮动动画
          cube.position.y = cube.userData.initialY + Math.sin(time * cube.userData.floatSpeed) * cube.userData.floatRange;

          // 更新shader时间
          if (cube.material.uniforms && cube.material.uniforms.time) {
            cube.material.uniforms.time.value = time;
          }
        });
      }

      // 更新能量环动画
      if (this.energyRings) {
        this.energyRings.children.forEach(child => {
          if (child.userData.rotationSpeed) {
            // 旋转动画
            child.rotation.x += child.userData.rotationSpeed.x;
            child.rotation.y += child.userData.rotationSpeed.y;
            child.rotation.z += child.userData.rotationSpeed.z;

            // 更新shader时间
            if (child.material.uniforms && child.material.uniforms.time) {
              child.material.uniforms.time.value = time;
            }
          }
        });

        // 脉动动画
        const pulseFactor = 0.8 + Math.sin(time * 2) * 0.2;
        this.energyRings.scale.set(pulseFactor, pulseFactor, pulseFactor);
      }

      // 更新浮动图标动画
      if (this.floatingIcons) {
        this.floatingIcons.children.forEach(icon => {
          // 旋转动画
          icon.rotation.x += icon.userData.rotationSpeed.x;
          icon.rotation.y += icon.userData.rotationSpeed.y;
          icon.rotation.z += icon.userData.rotationSpeed.z;

          // 浮动动画
          icon.position.y = icon.userData.initialY + Math.sin(time * icon.userData.floatSpeed * 10) * icon.userData.floatRange;

          // 更新shader时间
          if (icon.material.uniforms && icon.material.uniforms.time) {
            icon.material.uniforms.time.value = time;
          }
        });
      }

      // 更新数据流线
      if (this.dataStreams) {
        this.dataStreams.children.forEach(stream => {
          if (stream.material.uniforms && stream.material.uniforms.time) {
            stream.material.uniforms.time.value = time;
          }
        });
      }

      // 更新HUD元素
      if (this.hudElements) {
        this.hudElements.children.forEach(panel => {
          // 轻微旋转
          panel.rotation.x += panel.userData.rotationSpeed.x;
          panel.rotation.y += panel.userData.rotationSpeed.y;
          panel.rotation.z += panel.userData.rotationSpeed.z;

          // 始终面向相机
          panel.lookAt(this.camera.position);

          // 添加轻微的漂浮动画
          panel.position.y += Math.sin(time + panel.position.x * 0.1) * 0.01;

          // 更新shader时间
          if (panel.material.uniforms && panel.material.uniforms.time) {
            panel.material.uniforms.time.value = time;
          }
        });
      }

      // 平滑移动相机
      const targetCamX = this.smoothedMouse.x * 3;
      const targetCamY = this.smoothedMouse.y * 2;
      this.camera.position.x += (targetCamX - this.camera.position.x) * 0.05;
      this.camera.position.y += (targetCamY - this.camera.position.y) * 0.05;
      this.camera.lookAt(this.scene.position);

      // 平滑移动光源
      if (this.textSideLights) {
        this.textSideLights.forEach(light => {
          if (light.userData.initialPosition) {
            const targetLightX = light.userData.initialPosition.x + this.smoothedMouse.x * 15;
            const targetLightY = light.userData.initialPosition.y + this.smoothedMouse.y * 15;
            light.position.x += (targetLightX - light.position.x) * 0.05;
            light.position.y += (targetLightY - light.position.y) * 0.05;
          }
        });
      }

      // 手动计算FPS
      const now = performance.now();
      if (now >= (this.lastFPSTime || 0) + 1000) {
        this.currentFPS = Math.round((this.frameCount * 1000) / (now - this.lastFPSTime));
        this.lastFPSTime = now;
        this.frameCount = 0;
      }

      this.composer.render();
    },
    simulateLoading() {
      const interval = setInterval(() => {
        this.loadingProgress += 1;
        if (this.loadingProgress >= 100) {
          clearInterval(interval);
          this.loadingComplete = true;
        }
      }, 30);
    },
    // 修改处理滚动事件的方法
    handleScroll(event) {
      if (this.loadingComplete && !this.isFadingOut) {
        this.isFadingOut = true; // 防止重复触发

        // 立即移除性能监控面板
        if (this.stats && this.stats.dom && this.stats.dom.parentElement) {
          this.stats.dom.parentElement.removeChild(this.stats.dom);
          this.stats = null;
        }
        if (this.fpsCheckInterval) {
          clearInterval(this.fpsCheckInterval);
          this.fpsCheckInterval = null;
        }

        this.setSplashShown();

        const tl = gsap.timeline({
          onComplete: () => {
            this.visible = false;
            this.enableBodyScroll();
            this.$root.$emit('splash-hidden');

            // 恢复之前的路径
            const lastPath = localStorage.getItem('lastPath');
            if (lastPath && lastPath !== '/' && this.$route.path === '/') {
              this.$router.push(lastPath);
              localStorage.removeItem('lastPath');
            }
            // 彻底停止动画循环，但不销毁渲染器
            if (this.animationFrame) {
              cancelAnimationFrame(this.animationFrame);
              this.animationFrame = null;
            }
            // if (this.renderer) this.renderer.dispose(); // 不应在此处销毁渲染器
          }
        });

        // 0. 在开始新动画之前，清除所有可能残留的动画
        gsap.killTweensOf('.splash-content, .tech-keywords');
        gsap.killTweensOf(this.camera.position);
        gsap.killTweensOf(this.scene.fog);
        gsap.killTweensOf(this.$refs.threeContainer);

        // 1. UI元素淡出
        tl.to('.splash-content, .tech-keywords', {
          duration: 0.7,
          opacity: 0,
          ease: 'power2.in'
        });

        // 2. 镜头推进，创造飞入效果
        tl.to(this.camera.position, {
          z: this.camera.position.z + 120,
          duration: 1.2,
          ease: 'power3.in'
        }, '>-0.5'); // 在上一个动画结束前0.5秒开始

        // 3. 雾效增强，配合镜头推进
        if (this.scene.fog) {
          tl.to(this.scene.fog, {
            density: 0.012,
            duration: 1.2,
            ease: 'power3.in'
          }, '<'); // 与镜头动画同步开始
        }

        // 4. 最后将整个画布淡出
        tl.to(this.$refs.threeContainer, {
          duration: 0.5,
          opacity: 0,
          ease: 'power1.in'
        }, '>-0.3');

        // 移除滚动监听
        window.removeEventListener('wheel', this.scrollListener);
        window.removeEventListener('touchmove', this.scrollListener);
      }
    },
    onMouseMove(event) {
      // 简单的节流处理，减少鼠标事件频率
      if (this._mouseThrottled) return;
      this._mouseThrottled = true;
      setTimeout(() => { this._mouseThrottled = false; }, 20); // 50fps的更新率

      // 计算鼠标位置
      this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
      this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

      // 射线检测
      this.raycaster.setFromCamera(this.mouse, this.camera);
      const intersects = this.raycaster.intersectObjects(this.interactiveObjects, true);

      // 查找与文字和其他物体的交点
      const textIntersect = intersects.find(i => this.textMeshes.includes(i.object));
      const otherIntersect = intersects.find(i => !this.textMeshes.includes(i.object));

      // --- 重置所有交互状态 ---
      document.body.style.cursor = 'default';
      // 重置文字悬停状态
      this.textFlows.forEach(flow => {
        if (flow.material.uniforms.isHovering !== undefined) {
          flow.material.uniforms.isHovering.value = 0.0;
        }
      });
      // 重置其他物体的高亮
      this.interactiveObjects.forEach(obj => {
        // 排除文字本身和没有emissive属性的对象
        if (!this.textMeshes.includes(obj) && obj.material && obj.material.emissive) {
          // 恢复到其初始的emissive颜色，而不是统一的颜色
          if (obj.userData.initialEmissive) {
            obj.material.emissive.copy(obj.userData.initialEmissive);
          } else {
            obj.material.emissive.set(0x000000); // 默认设置为黑色
          }
        }
      });

      // --- 应用新的交互状态 ---
      if (textIntersect) {
        document.body.style.cursor = 'pointer';
        // 更新文字着色器
        const intersectedMesh = textIntersect.object;
        const flowMesh = this.textFlows[this.textMeshes.indexOf(intersectedMesh)];
        if (flowMesh && flowMesh.material.uniforms.mousePos !== undefined) {
          flowMesh.material.uniforms.isHovering.value = 1.0;
          const localPoint = flowMesh.worldToLocal(textIntersect.point.clone());
          flowMesh.material.uniforms.mousePos.value.copy(localPoint);
        }
      } else if (otherIntersect) {
        document.body.style.cursor = 'pointer';
        // 高亮其他物体
        const intersectedObj = otherIntersect.object;
        if (intersectedObj.material && intersectedObj.material.emissive) {
          // 保存初始颜色（如果尚未保存）
          if (!intersectedObj.userData.initialEmissive) {
            intersectedObj.userData.initialEmissive = intersectedObj.material.emissive.clone();
          }
          intersectedObj.material.emissive.set(0x64b5f6); // 设置高亮色
        }
      }

      // 关键词交互效果 - 当鼠标靠近时放大并发光
      const keywords = document.querySelectorAll('.keyword');
      keywords.forEach(keyword => {
        const rect = keyword.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        const distanceX = Math.abs(event.clientX - centerX);
        const distanceY = Math.abs(event.clientY - centerY);
        const distance = Math.sqrt(distanceX * distanceX + distanceY * distanceY);

        // 当鼠标接近时放大并增强发光效果
        if (distance < 150) {
          const scale = 1.1 + (1 - distance / 150) * 0.3;
          const glow = 5 + (1 - distance / 150) * 15;

          keyword.style.transform = `scale(${scale})`;
          keyword.style.textShadow = `0 0 ${glow}px rgba(33, 150, 243, 1), 0 0 ${glow * 2}px rgba(33, 150, 243, 0.8)`;
          keyword.style.zIndex = 10;
        } else {
          keyword.style.transform = '';
          keyword.style.textShadow = '';
          keyword.style.zIndex = '';
        }
      });
    },
    onWindowResize() {
      // 添加去抖动处理，避免频繁重设大小
      if (this._resizeTimeout) {
        clearTimeout(this._resizeTimeout);
      }

      this._resizeTimeout = setTimeout(() => {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);

        // 根据设备性能动态调整分辨率
        const pixelRatio = this.hasReducedQuality ?
          1.0 : Math.min(window.devicePixelRatio, 1.5);
        this.renderer.setPixelRatio(pixelRatio);

        if (this.composer) {
          this.composer.setSize(window.innerWidth, window.innerHeight);
        }

        // 重新创建3D文本以适应新的屏幕尺寸
        if (this.font) {
          this.create3DText();
        }
      }, 250); // 250ms去抖动延迟
    },
    getKeywordStyle(index) {
      const opacity = Math.random() * 0.5 + 0.2;
      const size = Math.random() * 0.8 + 0.8;
      const delay = index * 0.1;

      return {
        opacity: opacity,
        transform: `scale(${size})`,
        animationDelay: `${delay}s`,
        left: `${Math.random() * 80 + 10}%`,
        top: `${Math.random() * 80 + 10}%`
      };
    },
    update() {
      // 在组件更新后设置动画循环
      this.animate();

      // 更新粒子流动的时间
      const updateParticleTime = () => {
        const time = performance.now() * 0.001;

        // 更新粒子系统的时间
        if (this.particles && this.particles.material.uniforms) {
          this.particles.material.uniforms.time.value = time;
        }

        // 更新银河系粒子的时间
        if (this.galaxyParticles && this.galaxyParticles.material.uniforms) {
          this.galaxyParticles.material.uniforms.time.value = time;
        }

        requestAnimationFrame(updateParticleTime);
      };

      updateParticleTime();
    },

    // 性能检测和动态质量调整
    checkPerformance() {
      if (!this.stats || this.hasReducedQuality) return;

      // 获取当前FPS
      const fps = this.currentFPS;

      // 如果FPS低于阈值，计数增加
      if (fps < 30) {
        this.lowFPSCount++;

        // 连续3次检测到低FPS，自动降低质量
        if (this.lowFPSCount >= 3) {
          this.reduceQuality();
          this.hasReducedQuality = true;
        }
      } else {
        // 重置计数器
        this.lowFPSCount = Math.max(0, this.lowFPSCount - 1);
      }
    },

    // 降低渲染质量以提高性能
    reduceQuality() {
      console.log('自动降低渲染质量以提高性能');

      // 降低像素比
      this.renderer.setPixelRatio(1.0);

      // 如果有后处理，降低其复杂度
      if (this.composer && this.composer.passes.length > 1) {
        // 降低Bloom效果复杂度
        const bloomPass = this.composer.passes.find(pass => pass instanceof UnrealBloomPass);
        if (bloomPass) {
          bloomPass.resolution.set(
            window.innerWidth * 0.5,
            window.innerHeight * 0.5
          );
          bloomPass.strength = 0.2;
          bloomPass.radius = 0.3;
          bloomPass.threshold = 1.0;
        }
      }

      // 如果粒子系统过于密集，减少可见粒子
      if (this.particles) {
        // 通过调整可见性来"减少"粒子，而不是实际重建几何体
        const geometry = this.particles.geometry;
        if (geometry.attributes.size) {
          const sizes = geometry.attributes.size.array;
          for (let i = 0; i < sizes.length; i += 2) {
            sizes[i] *= 0.5; // 减小一半粒子的大小，实质上使其不太可见
          }
          geometry.attributes.size.needsUpdate = true;
        }
      }

      // 隐藏高开销的装饰性元素
      if (this.flowingEdges) this.flowingEdges.visible = false;
      if (this.hudElements) this.hudElements.visible = false;
      if (this.dataStreams) this.dataStreams.visible = false;

      // 降低线条的复杂度
      if (this.dataStreams) {
        const children = [...this.dataStreams.children];
        // 移除一些数据流线以提高性能
        for (let i = 0; i < children.length; i += 2) {
          if (i < children.length) {
            this.dataStreams.remove(children[i]);
          }
        }
      }
    },
    createDataCubes() {
      const cubeCount = 12;
      const cubeGroup = new THREE.Group();
      cubeGroup.position.set(-25, 0, -15);

      for (let i = 0; i < cubeCount; i++) {
        // 创建不同大小的立方体
        const size = Math.random() * 1.5 + 0.5;
        const geometry = new THREE.BoxGeometry(size, size, size);

        // 创建全息材质
        const material = new THREE.ShaderMaterial({
          uniforms: {
            time: { value: 0 },
            color: { value: new THREE.Color(0x4fc3f7) }
          },
          vertexShader: `
            uniform float time;
            varying vec3 vPosition;
            varying vec2 vUv;
            varying vec3 vNormal;
            
            void main() {
              vPosition = position;
              vUv = uv;
              vNormal = normal;
              
              // 添加轻微的呼吸效果
              vec3 pos = position;
              float pulseFactor = sin(time * 0.5) * 0.05 + 1.0;
              pos *= pulseFactor;
              
              gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
            }
          `,
          fragmentShader: `
            uniform float time;
            uniform vec3 color;
            varying vec3 vPosition;
            varying vec2 vUv;
            varying vec3 vNormal;
            
            void main() {
              // 创建格子效果
              float gridX = step(0.95, mod(vUv.x * 10.0 + time * 0.2, 1.0));
              float gridY = step(0.95, mod(vUv.y * 10.0 + time * 0.2, 1.0));
              float grid = max(gridX, gridY);
              
              // 边缘发光
              float edge = 1.0 - dot(normalize(vNormal), normalize(vec3(0.0, 0.0, 1.0)));
              edge = pow(edge, 2.0) * 0.8;
              
              // 创建全息效果
              vec3 baseColor = color * (0.5 + 0.5 * sin(time + vPosition.x * 2.0));
              
              // 合并效果
              vec3 finalColor = mix(baseColor, vec3(1.0), grid * 0.5);
              float alpha = 0.2 + edge * 0.4 + grid * 0.4;
              
              gl_FragColor = vec4(finalColor, alpha);
            }
          `,
          transparent: true,
          blending: THREE.AdditiveBlending,
          side: THREE.DoubleSide,
          depthWrite: false
        });

        const cube = new THREE.Mesh(geometry, material);

        // 随机位置和旋转
        const radius = 6;
        const angle = (i / cubeCount) * Math.PI * 2;
        const height = (Math.random() - 0.5) * 8;

        cube.position.set(
          Math.cos(angle) * radius,
          height,
          Math.sin(angle) * radius
        );

        cube.rotation.set(
          Math.random() * Math.PI,
          Math.random() * Math.PI,
          Math.random() * Math.PI
        );

        // 储存各个立方体的动画参数
        cube.userData.rotationSpeed = {
          x: Math.random() * 0.01 - 0.005,
          y: Math.random() * 0.01 - 0.005,
          z: Math.random() * 0.01 - 0.005
        };

        cube.userData.floatSpeed = Math.random() * 0.01 + 0.005;
        cube.userData.floatRange = Math.random() * 0.5 + 0.5;
        cube.userData.initialY = height;

        cubeGroup.add(cube);
      }

      this.scene.add(cubeGroup);
      this.dataCubes = cubeGroup;
    },

    createEnergyRings() {
      const ringsGroup = new THREE.Group();
      ringsGroup.position.set(25, 0, -20);

      // 创建三个环
      const colors = [0x4fc3f7, 0x2196f3, 0x0d47a1];
      const radii = [4, 6, 8];

      for (let i = 0; i < 3; i++) {
        const ringGeometry = new THREE.TorusGeometry(radii[i], 0.15, 16, 100);

        const material = new THREE.ShaderMaterial({
          uniforms: {
            time: { value: 0 },
            color: { value: new THREE.Color(colors[i]) }
          },
          vertexShader: `
            uniform float time;
            varying vec3 vPosition;
            
            void main() {
              vPosition = position;
              
              vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
              gl_Position = projectionMatrix * mvPosition;
            }
          `,
          fragmentShader: `
            uniform float time;
            uniform vec3 color;
            varying vec3 vPosition;
            
            void main() {
              // 创建流动效果
              float flow = fract(atan(vPosition.y, vPosition.x) / (3.1415 * 2.0) + time * 0.2);
              
              // 脉冲波纹
              float pulse = smoothstep(0.0, 0.15, flow) * smoothstep(0.35, 0.2, flow);
              pulse += smoothstep(0.45, 0.6, flow) * smoothstep(0.8, 0.65, flow);
              
              // 最终颜色
              vec3 glowColor = mix(color, vec3(1.0), pulse * 0.7);
              float alpha = 0.4 + pulse * 0.6;
              
              gl_FragColor = vec4(glowColor, alpha);
            }
          `,
          transparent: true,
          blending: THREE.AdditiveBlending,
          side: THREE.DoubleSide,
          depthWrite: false
        });

        const ring = new THREE.Mesh(ringGeometry, material);

        // 随机旋转
        ring.rotation.x = Math.PI / 2;
        ring.rotation.y = Math.random() * Math.PI / 4;
        ring.rotation.z = Math.random() * Math.PI / 4;

        // 储存动画参数
        ring.userData.rotationSpeed = {
          x: 0,
          y: 0.002 * (i + 1),
          z: 0.003 * (i + 1)
        };

        ringsGroup.add(ring);
      }

      // 添加中心光源
      const centerLight = new THREE.PointLight(0x4fc3f7, 2, 15);
      centerLight.position.set(0, 0, 0);
      ringsGroup.add(centerLight);

      // 添加光球中心
      const sphereGeometry = new THREE.SphereGeometry(1, 16, 16);
      const sphereMaterial = new THREE.MeshBasicMaterial({
        color: 0x4fc3f7,
        transparent: true,
        opacity: 0.8,
        blending: THREE.AdditiveBlending
      });

      const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
      ringsGroup.add(sphere);

      this.scene.add(ringsGroup);
      this.energyRings = ringsGroup;
    },

    createFloatingIcons() {
      const iconGroup = new THREE.Group();

      // 创建几个代表技术的几何体图标
      const iconShapes = [
        new THREE.TetrahedronGeometry(1, 0), // 三角锥
        new THREE.OctahedronGeometry(1, 0),  // 八面体
        new THREE.DodecahedronGeometry(1, 0), // 十二面体
        new THREE.TorusKnotGeometry(0.8, 0.3, 64, 8, 2, 3) // 环面结
      ];

      const positions = [
        new THREE.Vector3(-35, 15, -25),
        new THREE.Vector3(35, 12, -30),
        new THREE.Vector3(-30, -10, -20),
        new THREE.Vector3(40, -15, -35)
      ];

      const colors = [0x4fc3f7, 0x2196f3, 0x03a9f4, 0x00bcd4];

      for (let i = 0; i < iconShapes.length; i++) {
        const material = new THREE.ShaderMaterial({
          uniforms: {
            time: { value: 0 },
            baseColor: { value: new THREE.Color(colors[i]) }
          },
          vertexShader: `
            uniform float time;
            varying vec3 vNormal;
            varying vec3 vPosition;
            
            void main() {
              vNormal = normal;
              gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
          `,
          fragmentShader: `
            uniform float time;
            uniform vec3 baseColor;
            varying vec3 vNormal;
            varying vec3 vPosition;
            
            void main() {
              // 全息风格纹理
              float pattern1 = abs(sin(vPosition.x * 10.0 + time));
              float pattern2 = abs(sin(vPosition.y * 10.0 + time * 1.5));
              float pattern3 = abs(sin(vPosition.z * 10.0 + time * 0.7));
              
              float pattern = max(max(pattern1, pattern2), pattern3);
              pattern = smoothstep(0.3, 0.7, pattern);
              
              // 边缘光晕
              float rim = pow(1.0 - abs(dot(vNormal, vec3(0, 0, 1))), 2.0);
              
              // 颜色变化
              vec3 glowColor = baseColor * (0.5 + 0.5 * sin(time + vPosition.x * 2.0));
              vec3 finalColor = mix(glowColor, vec3(1.0), pattern * 0.5 + rim * 0.3);
              
              gl_FragColor = vec4(finalColor, 0.3 + pattern * 0.4 + rim * 0.3);
            }
          `,
          transparent: true,
          blending: THREE.AdditiveBlending,
          side: THREE.DoubleSide,
          depthWrite: false
        });

        const icon = new THREE.Mesh(iconShapes[i], material);
        icon.position.copy(positions[i]);

        // 储存动画参数
        icon.userData.rotationSpeed = {
          x: Math.random() * 0.01 - 0.005,
          y: Math.random() * 0.01 - 0.005,
          z: Math.random() * 0.01 - 0.005
        };

        icon.userData.floatSpeed = Math.random() * 0.001 + 0.0005;
        icon.userData.floatRange = Math.random() * 0.5 + 0.5;
        icon.userData.initialY = positions[i].y;

        iconGroup.add(icon);
      }

      this.scene.add(iconGroup);
      this.floatingIcons = iconGroup;
    },
    createDataStreamLines() {
      const streamGroup = new THREE.Group();
      const lineCount = 18; // 减少线条数量，优化性能
      const pointsPerLine = 80; // 减少每条线上的点数，降低几何体复杂度

      // 创建样条曲线
      for (let i = 0; i < lineCount; i++) {
        // 创建曲线上的点
        const points = [];
        const curveLength = 40 + Math.random() * 30; // 增加长度变化
        const startX = -60 + Math.random() * 120; // 扩大范围
        const startY = -40 + Math.random() * 80; // 扩大范围
        const startZ = -60 + Math.random() * 40; // 扩大范围

        // 决定流线方向
        const dirX = Math.random() * 2 - 1;
        const dirY = Math.random() * 2 - 1;
        const dirZ = Math.random() * 2 - 1;

        // 创建曲线上的点
        for (let j = 0; j < pointsPerLine; j++) {
          const t = j / (pointsPerLine - 1);

          // 添加一些随机性来创建曲线
          const waveX = Math.sin(t * Math.PI * (4 + Math.random() * 2)) * (1 + Math.random() * 1.5);
          const waveY = Math.sin(t * Math.PI * (3 + Math.random() * 2)) * (1 + Math.random() * 1.5);
          const waveZ = Math.sin(t * Math.PI * (5 + Math.random() * 2)) * (1 + Math.random() * 1.5);

          points.push(new THREE.Vector3(
            startX + t * curveLength * dirX + waveX,
            startY + t * curveLength * dirY + waveY,
            startZ + t * curveLength * dirZ + waveZ
          ));
        }

        // 创建曲线
        const curve = new THREE.CatmullRomCurve3(points);
        const curvePoints = curve.getPoints(200);
        const curveGeometry = new THREE.BufferGeometry().setFromPoints(curvePoints);

        // 为每个点添加大小和颜色属性
        const particleCount = curvePoints.length;
        const sizes = new Float32Array(particleCount);
        const colors = new Float32Array(particleCount * 3);

        // 设置颜色渐变
        // 随机选择颜色组合以增加多样性
        const colorOptions = [
          { start: new THREE.Color(0x0d47a1), end: new THREE.Color(0x4fc3f7) }, // 蓝色系
          { start: new THREE.Color(0x1a237e), end: new THREE.Color(0x7986cb) }, // 靛蓝系
          { start: new THREE.Color(0x311b92), end: new THREE.Color(0x9575cd) }, // 紫色系
          { start: new THREE.Color(0x004d40), end: new THREE.Color(0x4db6ac) }  // 青色系
        ];

        const colorChoice = Math.floor(Math.random() * colorOptions.length);
        const startColor = colorOptions[colorChoice].start;
        const endColor = colorOptions[colorChoice].end;

        for (let j = 0; j < particleCount; j++) {
          // 粒子大小，流线的头部较大
          const t = j / particleCount;
          sizes[j] = (1 - t) * 0.5 + 0.1; // 头部较大

          // 颜色渐变
          const color = new THREE.Color().lerpColors(startColor, endColor, t);
          colors[j * 3] = color.r;
          colors[j * 3 + 1] = color.g;
          colors[j * 3 + 2] = color.b;
        }

        curveGeometry.setAttribute('size', new THREE.Float32BufferAttribute(sizes, 1));
        curveGeometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));

        // 创建着色器材质
        const lineMaterial = new THREE.ShaderMaterial({
          uniforms: {
            time: { value: 0 },
            speed: { value: 0.3 + Math.random() * 1.5 } // 增加速度变化
          },
          vertexShader: `
            attribute float size;
            attribute vec3 color;
            uniform float time;
            uniform float speed;
            varying vec3 vColor;
            varying float vAlpha;
            
            void main() {
              vColor = color;
              
              // 计算粒子在流线上的位置
              float particleProgress = (speed * time + float(gl_VertexID) / 200.0);
              float modProgress = mod(particleProgress, 1.0);
              
              // 透明度，创建流动的尾巴效果
              vAlpha = 1.0 - modProgress;
              
              // 如果粒子不在当前的流线段上，就隐藏它
              if (modProgress > 0.3) {
                vAlpha = 0.0;
              }
              
              gl_PointSize = size * 2.5; // 增大点大小
              gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
          `,
          fragmentShader: `
            varying vec3 vColor;
            varying float vAlpha;
            
            void main() {
              if (vAlpha <= 0.0) discard;
              
              // 创建柔和的圆形粒子
              float dist = length(gl_PointCoord - vec2(0.5));
              if (dist > 0.5) discard;
              
              float alpha = smoothstep(0.5, 0.0, dist) * vAlpha;
              gl_FragColor = vec4(vColor, alpha);
            }
          `,
          transparent: true,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        const dataStream = new THREE.Points(curveGeometry, lineMaterial);
        dataStream.frustumCulled = false; // 防止视锥体裁剪
        streamGroup.add(dataStream);
      }

      // 创建一些流动的环形路径
      const ringCount = 5;
      for (let i = 0; i < ringCount; i++) {
        // 创建环形路径
        const radius = 20 + Math.random() * 30;
        const circlePoints = [];
        const segments = 100;

        // 随机位置和旋转
        const center = new THREE.Vector3(
          (Math.random() - 0.5) * 80,
          (Math.random() - 0.5) * 60,
          (Math.random() - 0.5) * 60
        );

        // 创建稍微倾斜的圆形
        const tiltX = Math.random() * Math.PI;
        const tiltY = Math.random() * Math.PI;

        for (let j = 0; j <= segments; j++) {
          const theta = (j / segments) * Math.PI * 2;
          const x = radius * Math.cos(theta);
          const y = radius * Math.sin(theta);

          // 添加倾斜和波动
          const point = new THREE.Vector3(
            x,
            y * Math.cos(tiltX) + Math.sin(theta * 3) * 2,
            y * Math.sin(tiltX) + Math.sin(theta * 5) * 2
          );

          // 旋转点
          point.applyAxisAngle(new THREE.Vector3(0, 1, 0), tiltY);
          point.add(center);

          circlePoints.push(point);
        }

        const curve = new THREE.CatmullRomCurve3(circlePoints);
        const curvePoints = curve.getPoints(200);
        const curveGeometry = new THREE.BufferGeometry().setFromPoints(curvePoints);

        // 为每个点添加属性
        const particleCount = curvePoints.length;
        const sizes = new Float32Array(particleCount);
        const colors = new Float32Array(particleCount * 3);

        // 随机选择环形颜色
        const colorChoice = Math.random();
        let startColor, endColor;

        if (colorChoice < 0.3) {
          startColor = new THREE.Color(0x00bcd4);
          endColor = new THREE.Color(0x80deea);
        } else if (colorChoice < 0.6) {
          startColor = new THREE.Color(0x3f51b5);
          endColor = new THREE.Color(0x9fa8da);
        } else {
          startColor = new THREE.Color(0x7b1fa2);
          endColor = new THREE.Color(0xce93d8);
        }

        for (let j = 0; j < particleCount; j++) {
          sizes[j] = 0.3 + Math.random() * 0.3;

          // 颜色渐变 - 沿环形变化
          const t = j / particleCount;
          const color = new THREE.Color().lerpColors(startColor, endColor, Math.sin(t * Math.PI * 2) * 0.5 + 0.5);
          colors[j * 3] = color.r;
          colors[j * 3 + 1] = color.g;
          colors[j * 3 + 2] = color.b;
        }

        curveGeometry.setAttribute('size', new THREE.Float32BufferAttribute(sizes, 1));
        curveGeometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));

        // 创建着色器材质
        const ringMaterial = new THREE.ShaderMaterial({
          uniforms: {
            time: { value: 0 },
            speed: { value: 0.1 + Math.random() * 0.3 }
          },
          vertexShader: `
            attribute float size;
            attribute vec3 color;
            uniform float time;
            uniform float speed;
            varying vec3 vColor;
            varying float vAlpha;
            
            void main() {
              vColor = color;
              
              float loopLength = 200.0;
              float particleId = mod(float(gl_VertexID) + time * speed * loopLength, loopLength);
              float particleProgress = particleId / loopLength;
              
              // 创建波动效果，让粒子有脉动感
              float wave = sin(particleProgress * 3.14159 * 8.0) * 0.5 + 0.5;
              vAlpha = wave * 0.7 + 0.3;
              
              // 尺寸根据波动变化
              gl_PointSize = size * (wave * 1.0 + 1.0) * 2.0;
              gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
          `,
          fragmentShader: `
            varying vec3 vColor;
            varying float vAlpha;
            
            void main() {
              // 创建柔和的圆形粒子
              float dist = length(gl_PointCoord - vec2(0.5));
              if (dist > 0.5) discard;
              
              float alpha = smoothstep(0.5, 0.0, dist) * vAlpha;
              gl_FragColor = vec4(vColor, alpha);
            }
          `,
          transparent: true,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        const ringStream = new THREE.Points(curveGeometry, ringMaterial);
        ringStream.frustumCulled = false;
        streamGroup.add(ringStream);
      }

      this.scene.add(streamGroup);
      this.dataStreams = streamGroup;
    },

    createFlowingEdges() {
      // 创建页面边缘的流动线条
      const edgeGroup = new THREE.Group();

      // 创建四边框架
      const frameSize = {
        width: 90,
        height: 50,
        depth: 40
      };

      // 创建线条的点
      const createEdgeLine = (start, end, segments, color) => {
        const points = [];
        for (let i = 0; i <= segments; i++) {
          const t = i / segments;
          points.push(new THREE.Vector3(
            start.x + (end.x - start.x) * t,
            start.y + (end.y - start.y) * t,
            start.z + (end.z - start.z) * t
          ));
        }

        const curve = new THREE.CatmullRomCurve3(points);
        const geometry = new THREE.TubeGeometry(curve, 100, 0.1, 8, false);

        const material = new THREE.ShaderMaterial({
          uniforms: {
            time: { value: 0 },
            color: { value: new THREE.Color(color) }
          },
          vertexShader: `
            uniform float time;
            varying vec3 vPosition;
            varying float vLength;
            
            void main() {
              vPosition = position;
              vLength = length(position);
              gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
          `,
          fragmentShader: `
            uniform float time;
            uniform vec3 color;
            varying vec3 vPosition;
            varying float vLength;
            
            void main() {
              // 创建流动效果
              float flow = fract(vLength * 0.05 - time * 0.5);
              
              // 脉冲波纹
              float pulse = smoothstep(0.0, 0.2, flow) * smoothstep(0.6, 0.4, flow);
              
              // 最终颜色
              vec3 glowColor = mix(color, vec3(1.0), pulse * 0.7);
              float alpha = 0.3 + pulse * 0.7;
              
              gl_FragColor = vec4(glowColor, alpha);
            }
          `,
          transparent: true,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        return new THREE.Mesh(geometry, material);
      };

      // 边框顶点
      const corners = [
        new THREE.Vector3(-frameSize.width / 2, -frameSize.height / 2, -frameSize.depth / 2), // 左下后
        new THREE.Vector3(frameSize.width / 2, -frameSize.height / 2, -frameSize.depth / 2),  // 右下后
        new THREE.Vector3(frameSize.width / 2, frameSize.height / 2, -frameSize.depth / 2),   // 右上后
        new THREE.Vector3(-frameSize.width / 2, frameSize.height / 2, -frameSize.depth / 2),  // 左上后
        new THREE.Vector3(-frameSize.width / 2, -frameSize.height / 2, frameSize.depth / 2),  // 左下前
        new THREE.Vector3(frameSize.width / 2, -frameSize.height / 2, frameSize.depth / 2),   // 右下前
        new THREE.Vector3(frameSize.width / 2, frameSize.height / 2, frameSize.depth / 2),    // 右上前
        new THREE.Vector3(-frameSize.width / 2, frameSize.height / 2, frameSize.depth / 2)    // 左上前
      ];

      // 创建边线
      // 后平面
      edgeGroup.add(createEdgeLine(corners[0], corners[1], 50, 0x4fc3f7));
      edgeGroup.add(createEdgeLine(corners[1], corners[2], 50, 0x2196f3));
      edgeGroup.add(createEdgeLine(corners[2], corners[3], 50, 0x0d47a1));
      edgeGroup.add(createEdgeLine(corners[3], corners[0], 50, 0x4fc3f7));

      // 前平面
      edgeGroup.add(createEdgeLine(corners[4], corners[5], 50, 0x4fc3f7));
      edgeGroup.add(createEdgeLine(corners[5], corners[6], 50, 0x2196f3));
      edgeGroup.add(createEdgeLine(corners[6], corners[7], 50, 0x0d47a1));
      edgeGroup.add(createEdgeLine(corners[7], corners[4], 50, 0x4fc3f7));

      // 连接线
      edgeGroup.add(createEdgeLine(corners[0], corners[4], 30, 0x00bcd4));
      edgeGroup.add(createEdgeLine(corners[1], corners[5], 30, 0x00bcd4));
      edgeGroup.add(createEdgeLine(corners[2], corners[6], 30, 0x00bcd4));
      edgeGroup.add(createEdgeLine(corners[3], corners[7], 30, 0x00bcd4));

      // 添加额外的对角线
      edgeGroup.add(createEdgeLine(corners[0], corners[6], 60, 0x9c27b0));
      edgeGroup.add(createEdgeLine(corners[1], corners[7], 60, 0x9c27b0));

      // 添加动态数据点 - 在框架上随机移动的点
      const dataPointCount = 40;
      const dataPointGeometry = new THREE.SphereGeometry(0.3, 8, 8);

      for (let i = 0; i < dataPointCount; i++) {
        // 为每个数据点随机选择一条边线路径
        const startCornerIdx = Math.floor(Math.random() * 8);
        const endCornerIdx = (startCornerIdx + 1 + Math.floor(Math.random() * 6)) % 8;

        const dataPointMaterial = new THREE.ShaderMaterial({
          uniforms: {
            time: { value: 0 },
            speed: { value: 0.2 + Math.random() * 0.5 },
            startPoint: { value: corners[startCornerIdx] },
            endPoint: { value: corners[endCornerIdx] },
            color: { value: new THREE.Color(0x4fc3f7) }
          },
          vertexShader: `
            uniform float time;
            uniform float speed;
            uniform vec3 startPoint;
            uniform vec3 endPoint;
            
            void main() {
              // 计算点在路径上的位置
              float progress = fract(time * speed);
              vec3 pathPosition = mix(startPoint, endPoint, progress);
              
              // 应用偏移
              vec3 newPosition = position + pathPosition;
              
              gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0);
            }
          `,
          fragmentShader: `
            uniform vec3 color;
            
            void main() {
              // 创建点的发光效果
              vec3 glowColor = color;
              float alpha = 0.8;
              
              gl_FragColor = vec4(glowColor, alpha);
            }
          `,
          transparent: true,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        const dataPoint = new THREE.Mesh(dataPointGeometry, dataPointMaterial);
        edgeGroup.add(dataPoint);
      }

      // 添加到场景
      this.scene.add(edgeGroup);
      this.flowingEdges = edgeGroup;
    },

    createHUDElements() {
      // 创建2D图层覆盖在3D场景上
      const hudGroup = new THREE.Group();
      hudGroup.position.z = -30;

      // 创建几个HUD面板
      const panelCount = 5;
      const panelPositions = [
        new THREE.Vector3(-40, 20, 0),
        new THREE.Vector3(40, 20, 0),
        new THREE.Vector3(-40, -20, 0),
        new THREE.Vector3(40, -20, 0),
        new THREE.Vector3(0, 35, 0)
      ];

      const panelSizes = [
        new THREE.Vector2(15, 10),
        new THREE.Vector2(15, 10),
        new THREE.Vector2(15, 10),
        new THREE.Vector2(15, 10),
        new THREE.Vector2(25, 8)
      ];

      for (let i = 0; i < panelCount; i++) {
        // 创建面板背景
        const panelGeometry = new THREE.PlaneGeometry(panelSizes[i].x, panelSizes[i].y);

        const panelMaterial = new THREE.ShaderMaterial({
          uniforms: {
            time: { value: 0 },
            resolution: { value: new THREE.Vector2(panelSizes[i].x, panelSizes[i].y) },
            color: { value: new THREE.Color(0x0d47a1) }
          },
          vertexShader: `
            varying vec2 vUv;
            
            void main() {
              vUv = uv;
              gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
          `,
          fragmentShader: `
            uniform float time;
            uniform vec2 resolution;
            uniform vec3 color;
            varying vec2 vUv;
            
            float drawLine(vec2 p1, vec2 p2, vec2 uv, float thickness) {
              vec2 dir = p2 - p1;
              vec2 norm = normalize(vec2(dir.y, -dir.x));
              float dist = abs(dot(uv - p1, norm));
              float along = dot(uv - p1, dir) / dot(dir, dir);
              if (along >= 0.0 && along <= 1.0 && dist < thickness) {
                return 1.0 - smoothstep(thickness * 0.5, thickness, dist);
              }
              return 0.0;
            }
            
            float drawBox(vec2 bottomLeft, vec2 topRight, vec2 uv, float thickness) {
              float left = drawLine(bottomLeft, vec2(bottomLeft.x, topRight.y), uv, thickness);
              float right = drawLine(vec2(topRight.x, bottomLeft.y), topRight, uv, thickness);
              float top = drawLine(vec2(bottomLeft.x, topRight.y), topRight, uv, thickness);
              float bottom = drawLine(bottomLeft, vec2(topRight.x, bottomLeft.y), uv, thickness);
              return max(max(left, right), max(top, bottom));
            }
            
            void main() {
              // 坐标系转换
              vec2 uv = vUv;
              
              // 创建边框
              float frameThickness = 0.003;
              float frame = drawBox(vec2(0.03, 0.03), vec2(0.97, 0.97), uv, frameThickness);
              
              // 创建扫描线效果
              float scanline = step(0.99, fract((uv.y + time * 0.1) * 30.0)) * 0.05;
              
              // 创建角落装饰
              float cornerSize = 0.05;
              float cornerThickness = 0.005;
              
              // 左上角
              float topLeft = drawLine(vec2(0.03, 0.03 + cornerSize), vec2(0.03, 0.03), uv, cornerThickness)
                            + drawLine(vec2(0.03, 0.03), vec2(0.03 + cornerSize, 0.03), uv, cornerThickness);
                            
              // 右上角
              float topRight = drawLine(vec2(0.97 - cornerSize, 0.03), vec2(0.97, 0.03), uv, cornerThickness)
                             + drawLine(vec2(0.97, 0.03), vec2(0.97, 0.03 + cornerSize), uv, cornerThickness);
                             
              // 左下角
              float bottomLeft = drawLine(vec2(0.03, 0.97), vec2(0.03, 0.97 - cornerSize), uv, cornerThickness)
                               + drawLine(vec2(0.03, 0.97), vec2(0.03 + cornerSize, 0.97), uv, cornerThickness);
                               
              // 右下角
              float bottomRight = drawLine(vec2(0.97 - cornerSize, 0.97), vec2(0.97, 0.97), uv, cornerThickness)
                                + drawLine(vec2(0.97, 0.97), vec2(0.97, 0.97 - cornerSize), uv, cornerThickness);
              
              float corners = max(max(topLeft, topRight), max(bottomLeft, bottomRight));
              
              // 创建一些数据可视化的随机线条
              float dataLines = 0.0;
              for (int j = 0; j < 5; j++) {
                float y = 0.2 + float(j) * 0.15;
                float xOffset = sin(time + float(j)) * 0.1;
                float lineX = mod(time * (0.1 + float(j) * 0.05) + float(j) * 0.2, 1.0);
                
                // 创建波形数据线
                for (int k = 0; k < 10; k++) {
                  float segmentX = float(k) * 0.1;
                  float height = sin(time * 2.0 + float(j) + float(k) * 0.5) * 0.03;
                  
                  if (lineX > segmentX && lineX < segmentX + 0.1) {
                    float t = (lineX - segmentX) / 0.1;
                    float prevHeight = sin(time * 2.0 + float(j) + float(k-1) * 0.5) * 0.03;
                    float nextHeight = sin(time * 2.0 + float(j) + float(k+1) * 0.5) * 0.03;
                    
                    float currentY = y + mix(prevHeight, height, t);
                    float targetY = y + mix(height, nextHeight, t);
                    
                    dataLines += drawLine(
                      vec2(0.1 + segmentX, currentY), 
                      vec2(0.1 + segmentX + 0.1, targetY), 
                      uv, 0.002
                    );
                  }
                }
              }
              
              // 基本图层
              float alpha = 0.3 + frame * 0.5 + corners * 0.7 + dataLines * 0.7;
              vec3 baseColor = color * (0.3 + 0.7 * uv.y);
              
              // 最终颜色
              vec3 finalColor = baseColor;
              finalColor += vec3(0.1, 0.3, 0.6) * scanline;
              finalColor += vec3(0.5, 0.8, 1.0) * (frame + corners + dataLines);
              
              gl_FragColor = vec4(finalColor, alpha);
            }
          `,
          transparent: true,
          blending: THREE.AdditiveBlending,
          depthWrite: false,
          side: THREE.DoubleSide
        });

        const panel = new THREE.Mesh(panelGeometry, panelMaterial);
        panel.position.copy(panelPositions[i]);
        panel.lookAt(this.camera.position);

        // 添加一些随机旋转动画
        panel.userData.rotationSpeed = {
          x: (Math.random() - 0.5) * 0.001,
          y: (Math.random() - 0.5) * 0.001,
          z: (Math.random() - 0.5) * 0.001
        };

        hudGroup.add(panel);
      }

      this.scene.add(hudGroup);
      this.hudElements = hudGroup;
    }
  }
};
</script>

<style lang="scss" scoped>
:global(html.splash-screen-active),
:global(html.splash-screen-active body) {
  overflow: hidden !important;
}

.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  overflow: hidden;
  transition: opacity 1s ease;
  background-color: #000;
  //禁止用户选择文字
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.fade-out {
  opacity: 0;
}

#three-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.splash-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 2;
  width: 100%;
  max-width: 800px;
  padding: 0 20px;
}

.logo-container {
  position: relative;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(33, 150, 243, 0.15);
}


/* 添加一些额外的视觉效果 */
.logo-text::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      rgba(79, 195, 247, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(79, 195, 247, 0) 100%);
  background-size: 200% 100%;
  mix-blend-mode: overlay;
  animation: lightSweep 5s infinite;
  z-index: 1;
  pointer-events: none;
}

@keyframes lightSweep {
  0% {
    background-position: -100% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

.logo-circle {
  width: 120px;
  height: 120px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  box-shadow: 0 0 15px rgba(79, 195, 247, 0.3);
  border: 2px solid rgba(79, 195, 247, 0.2);
  animation: pulse 3s infinite;
}

.logo-image {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.logo-text-wrapper {
  position: relative;
}

.logo-text {
  font-family: 'Orbitron', sans-serif;
  font-size: 4.5rem;
  font-weight: 700;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 3px;
  margin: 0;
  padding: 0;
  position: relative;
  z-index: 2;
  text-shadow:
    0 0 10px rgba(33, 150, 243, 0.7),
    0 0 20px rgba(33, 150, 243, 0.5),
    0 0 30px rgba(33, 150, 243, 0.3),
    0 0 40px rgba(33, 150, 243, 0.1);
  background: linear-gradient(90deg,
      #00ffff 0%,
      #4fc3f7 25%,
      #ffffff 50%,
      #4fc3f7 75%,
      #00ffff 100%);
  //   background: linear-gradient(90deg, #00ffff, #ffffff, #00ffff);
  //   background-size: 400% 100%;
  background-size: 200% auto;
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientFlow 8s linear infinite;
}

.logo-text-shadow {
  position: absolute;
  top: 0;
  left: 0;
  font-family: 'Orbitron', sans-serif;
  font-size: 4.5rem;
  font-weight: 900;
  color: rgba(156, 39, 176, 0.2);
  text-transform: uppercase;
  letter-spacing: 3px;
  margin: 0;
  padding: 0;
  z-index: 1;
  filter: blur(8px);
  transform: translateY(2px);
}

.logo-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  transform: translateX(-100%);
  animation: shine 5s infinite;
}

.loading-bar-container {
  margin: 30px auto;
  position: relative;
  border: 1px solid rgba(79, 195, 247, 0.3);
  padding: 15px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 10px rgba(79, 195, 247, 0.2);
}

.loading-bar-label {
  position: absolute;
  top: -10px;
  left: 15px;
  background: rgba(0, 0, 0, 0.8);
  padding: 0 10px;
  font-size: 0.9rem;
  color: #4fc3f7;
  letter-spacing: 1px;
}

.loading-details {
  margin-top: 10px;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
  text-align: left;
  padding-left: 5px;
}

.blink {
  animation: blink-animation 1s steps(2, start) infinite;
}

@keyframes blink-animation {
  to {
    visibility: hidden;
  }
}

.loading-bar {
  width: 300px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  margin: 30px auto;
  position: relative;
  overflow: hidden;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, #2196f3, #9c27b0);
  border-radius: 2px;
  position: relative;
  transition: width 0.3s ease;
}

.loading-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform: translateX(-100%);
  animation: loadingShine 1.5s infinite;
}

.loading-text {
  position: absolute;
  top: -25px;
  right: 0;
  font-size: 14px;
  color: #fff;
  text-shadow: 0 0 5px rgba(33, 150, 243, 0.5);
}

.tagline {
  margin: 30px 0;
  position: relative;
  padding: 15px;
  /* 降低背景不透明度 */
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(156, 39, 176, 0.1);
}

.tagline-text {
  font-size: 2rem;
  font-weight: 500;
  color: #ffffff;
  letter-spacing: 2px;
  background: linear-gradient(90deg,
      #4fc3f7 0%,
      rgb(209, 191, 209) 50%,
      #4fc3f7 100%);
  background-size: 200% auto;
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientFlow 8s linear infinite;
  /* 减弱文字阴影 */
  text-shadow:
    0 0 10px rgba(156, 39, 176, 0.2),
    0 0 20px rgba(156, 39, 176, 0.1);
  //   text-shadow: 
  //     0 0 10px rgba(156, 39, 176, 0.5),
  //     0 0 20px rgba(156, 39, 176, 0.3),
  //     0 0 30px rgba(156, 39, 176, 0.1);
  animation: fadeIn 2s;
}

.tagline {
  background: transparent;
  box-shadow: none;
}

/* 添加流光动画 */
@keyframes shine {
  from {
    background-position: 200% center;
  }

  to {
    background-position: -200% center;
  }
}

/* 添加文字悬浮效果 */
.logo-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: -75%;
  width: 50%;
  height: 100%;
  background: linear-gradient(to right,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 100%);
  transform: skewX(-25deg);
  animation: flowing 3s infinite;
}

@keyframes flowing {
  from {
    left: -75%;
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  to {
    left: 125%;
    opacity: 0;
  }
}

.tagline-underline {
  width: 50%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #ff00ff, transparent);
  margin: 10px auto 0;
  animation: pulse 3s infinite;
}

.scroll-indicator {
  margin-top: 50px;
  animation: fadeIn 2s;
}

.scroll-indicator span {
  display: block;
  margin-bottom: 15px;
  font-size: 1.2rem;
  color: #ffffff;
  letter-spacing: 1px;
  background: linear-gradient(90deg,
      rgba(255, 255, 255, 0.8) 0%,
      rgba(255, 255, 255, 1) 50%,
      rgba(255, 255, 255, 0.8) 100%);
  background-size: 200% auto;
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientFlow 8s linear infinite;
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.5),
    0 0 20px rgba(255, 255, 255, 0.3);
  //   background: rgba(0, 0, 0, 0.1);
  padding: 10px 20px;
  border-radius: 30px;
  display: inline-block;
}

.arrow-down {
  width: 20px;
  height: 20px;
  border-right: 3px solid #ffffff;
  border-bottom: 3px solid #ffffff;
  margin: 0 auto;
  transform: rotate(45deg);
  animation: bounce 2s infinite;
  box-shadow:
    0 0 10px rgba(255, 255, 255, 1),
    0 0 20px rgba(255, 255, 255, 0.8);
}

/* 为了保持文字可读性，可以添加轻微的文字描边 */
.logo-text,
.tagline-text {
  -webkit-text-stroke: 0.5px rgba(255, 255, 255, 0.1);
}

.tech-keywords {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.keyword {
  position: absolute;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.2rem;
  font-weight: 500;
  text-shadow:
    0 0 5px rgba(33, 150, 243, 1),
    0 0 10px rgba(33, 150, 243, 0.8);
  animation: keywordFloat 20s infinite linear;
  white-space: nowrap;
  letter-spacing: 1px;
  background: rgba(0, 0, 0, 0.3);
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid rgba(33, 150, 247, 0.3);
  box-shadow: 0 0 10px rgba(33, 150, 247, 0.3);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(33, 150, 243, 0.3), transparent);
    animation: keywordSweep 2s infinite;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }

  50% {
    transform: scale(1.1);
    opacity: 1;
  }

  100% {
    transform: scale(1);
    opacity: 0.5;
  }
}

@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }

  100% {
    background-position: 400% 50%;
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }

  20%,
  100% {
    transform: translateX(100%);
  }
}

@keyframes loadingShine {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes letterGlow {

  0%,
  100% {
    text-shadow: 0 0 10px rgba(33, 150, 243, 0.8);
    transform: scale(1);
  }

  50% {
    text-shadow: 0 0 20px rgba(156, 39, 176, 0.8), 0 0 30px rgba(156, 39, 176, 0.5);
    transform: scale(1.1);
  }
}

@keyframes charPulse {

  0%,
  100% {
    opacity: 0.8;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: rotate(45deg) translateY(0);
  }

  40% {
    transform: rotate(45deg) translateY(-10px);
  }

  60% {
    transform: rotate(45deg) translateY(-5px);
  }
}

@keyframes keywordFloat {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 0;
  }

  10% {
    opacity: var(--opacity, 0.2);
  }

  90% {
    opacity: var(--opacity, 0.2);
  }

  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

@keyframes keywordSweep {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

@media (max-width: 768px) {

  .logo-text,
  .logo-text-shadow {
    font-size: 2.8rem;
  }

  .loading-bar {
    width: 250px;
  }

  .tagline-text {
    font-size: 1.5rem;
  }
}

// 添加霓虹边框效果
.neon-corners {
  position: absolute;
  width: 100px;
  height: 100px;
  pointer-events: none;
  z-index: 10;
}

.neon-corners::before,
.neon-corners::after {
  content: '';
  position: absolute;
  background: linear-gradient(to right, #4fc3f7, #00bcd4);
  box-shadow: 0 0 15px #4fc3f7, 0 0 20px #4fc3f7, 0 0 30px #4fc3f7;
}

.top-left {
  top: 20px;
  left: 20px;
}

.top-left::before {
  width: 3px;
  height: 40px;
  left: 0;
  top: 0;
}

.top-left::after {
  width: 40px;
  height: 3px;
  left: 0;
  top: 0;
}

.top-right {
  top: 20px;
  right: 20px;
}

.top-right::before {
  width: 3px;
  height: 40px;
  right: 0;
  top: 0;
}

.top-right::after {
  width: 40px;
  height: 3px;
  right: 0;
  top: 0;
}

.bottom-left {
  bottom: 20px;
  left: 20px;
}

.bottom-left::before {
  width: 3px;
  height: 40px;
  left: 0;
  bottom: 0;
}

.bottom-left::after {
  width: 40px;
  height: 3px;
  left: 0;
  bottom: 0;
}

.bottom-right {
  bottom: 20px;
  right: 20px;
}

.bottom-right::before {
  width: 3px;
  height: 40px;
  right: 0;
  bottom: 0;
}

.bottom-right::after {
  width: 40px;
  height: 3px;
  right: 0;
  bottom: 0;
}

// 添加扫描线效果
.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(33, 150, 243, 0.3) 15%,
      rgba(33, 150, 243, 0.8) 50%,
      rgba(33, 150, 243, 0.3) 85%,
      transparent 100%);
  box-shadow: 0 0 8px rgba(33, 150, 243, 0.7);
  animation: scan 3s linear infinite;
  opacity: 0.7;
  z-index: 9;
  pointer-events: none;
}

@keyframes scan {
  0% {
    top: 0%;
  }

  100% {
    top: 100%;
  }
}

// 浮动数据面板
.floating-data-panels {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

.data-panel {
  position: absolute;
  width: 250px;
  background: rgba(0, 20, 40, 0.7);
  border: 1px solid rgba(79, 195, 247, 0.4);
  border-radius: 5px;
  padding: 0;
  box-shadow: 0 0 15px rgba(79, 195, 247, 0.3);
  backdrop-filter: blur(4px);
  overflow: hidden;
}

.left-panel {
  left: 30px;
  top: 50%;
  transform: translateY(-50%);
  animation: floatPanel 8s ease-in-out infinite;
}

.right-panel {
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  animation: floatPanel 8s ease-in-out infinite reverse;
}

.panel-header {
  background: linear-gradient(90deg, rgba(13, 71, 161, 0.7), rgba(33, 150, 243, 0.7));
  color: #fff;
  padding: 8px 15px;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.panel-header::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 30px;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.3));
  transform: skewX(-30deg);
  animation: headerGlow 4s infinite;
}

.panel-content {
  padding: 10px 15px;
}

.data-item {
  margin: 8px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
}

.data-label {
  color: rgba(255, 255, 255, 0.7);
  margin-right: 10px;
}

.data-value {
  color: rgba(79, 195, 247, 0.9);
  font-family: 'Consolas', monospace;
  text-shadow: 0 0 5px rgba(79, 195, 247, 0.5);
}

.data-value.highlight {
  color: rgba(156, 39, 176, 0.9);
  text-shadow: 0 0 5px rgba(156, 39, 176, 0.5);
}

.data-value.online {
  color: rgba(76, 175, 80, 0.9);
  text-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
}

.data-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-left: 10px;
}

.data-progress {
  height: 100%;
  background: linear-gradient(90deg, #2196f3, #9c27b0);
  border-radius: 3px;
  transition: width 1s ease;
}

@keyframes floatPanel {

  0%,
  100% {
    transform: translateY(-50%);
  }

  50% {
    transform: translateY(-50%) translateY(-20px);
  }
}

@keyframes headerGlow {

  0%,
  100% {
    right: -30px;
  }

  50% {
    right: 100%;
  }
}

// 优化加载区域
.loading-bar-container {
  margin: 30px auto;
  position: relative;
  border: 1px solid rgba(79, 195, 247, 0.3);
  padding: 15px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 10px rgba(79, 195, 247, 0.2);
  position: relative;
  overflow: hidden;
  max-width: 400px;
}

.loading-bar-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
      transparent 0%,
      rgba(79, 195, 247, 0.5) 50%,
      transparent 100%);
  animation: loadingBorderFlow 2s infinite;
}

.loading-bar-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(180deg,
      transparent 0%,
      rgba(79, 195, 247, 0.5) 50%,
      transparent 100%);
  animation: loadingBorderFlow 2s 0.5s infinite;
}

@keyframes loadingBorderFlow {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

// 使关键词更有科技感
.keyword {
  position: absolute;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.2rem;
  font-weight: 500;
  text-shadow:
    0 0 5px rgba(33, 150, 243, 1),
    0 0 10px rgba(33, 150, 243, 0.8);
  animation: keywordFloat 20s infinite linear;
  white-space: nowrap;
  letter-spacing: 1px;
  background: rgba(0, 10, 30, 0.4);
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid rgba(33, 150, 243, 0.3);
  box-shadow: 0 0 10px rgba(33, 150, 243, 0.3);
  backdrop-filter: blur(2px);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(33, 150, 243, 0.3), transparent);
    animation: keywordSweep 2s infinite;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid rgba(33, 150, 243, 0.5);
    border-radius: 4px;
    opacity: 0;
    transform: scale(1.1);
    animation: keywordPulse 2s infinite;
  }
}

@keyframes keywordPulse {
  0% {
    opacity: 0.7;
    transform: scale(1.1);
  }

  100% {
    opacity: 0;
    transform: scale(1.4);
  }
}

.logo-text span {
  position: relative;
  display: inline-block;
  animation: letterGlow 2s infinite;
  animation-delay: calc(0.1s * var(--i));
}
</style>