package com.kingcola.implatform.service;

import com.kingcola.dto.ai.AiStreamRequestDTO;
import com.kingcola.vo.ai.AiStreamResponseVO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * AI流式响应服务接口
 * 负责处理AI流式对话的业务逻辑和SSE连接管理
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public interface AiStreamService {

    /**
     * 创建AI流式对话
     * @param content 对话内容
     * @param chatType 聊天类型
     * @param receiverId 接收者ID
     * @param senderId 发送者ID（可选）
     * @param senderNickname 发送者昵称（可选）
     * @param sessionId 会话ID（可选）
     * @param token 认证token
     * @return SSE连接
     */
    SseEmitter createStreamChat(String content, String chatType, Long receiverId, 
                               Long senderId, String senderNickname, String sessionId, String token);

    /**
     * 获取活跃连接数
     * @return 连接数
     */
    int getActiveConnectionsCount();

    /**
     * 关闭指定会话连接
     * @param sessionId 会话ID
     * @return 是否成功
     */
    boolean closeConnection(String sessionId);

    /**
     * 向指定会话发送消息
     * @param sessionId 会话ID
     * @param response 响应消息
     */
    void sendToSession(String sessionId, AiStreamResponseVO response);

    /**
     * 获取指定会话的SSE连接
     * @param sessionId 会话ID
     * @return SSE连接
     */
    SseEmitter getConnection(String sessionId);
}
