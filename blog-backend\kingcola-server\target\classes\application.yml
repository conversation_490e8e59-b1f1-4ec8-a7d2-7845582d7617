spring:
  profiles:
    #    active: dev
    active: prod
  application:
    name: kingcola-blog
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  # 禁用JPA的open-in-view模式，避免警告日志
  # 虽然项目主要使用MyBatis Plus，但引入了JPA依赖，需要显式配置避免警告
  # jpa:
  #  open-in-view: false
file:
  expire: 30
############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 604800
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 自动续签 (如果此值为 true, 则在 token 最低活跃频率内（ `active-timeout` ）访问一次系统就会自动续签 token 的有效期)
  auto-renew: true
  # 是否输出操作日志
  is-log: true
#==============================日志调试配置=========================
logging:
  level:
    root: INFO
    com.kingcola: DEBUG
    org.springframework: WARN
    # Redis连接日志
    io.lettuce.core: WARN
    org.springframework.data.redis: WARN
    # 网络连接日志
    io.netty: WARN
    # MyBatis SQL日志 - 设置为DEBUG级别以显示SQL语句和查询结果
    org.apache.ibatis: DEBUG
    org.mybatis: DEBUG
    # MyBatis Plus SQL日志
    com.baomidou.mybatisplus: DEBUG
    # 显示SQL执行结果的详细日志
    com.kingcola.mapper: DEBUG
    # 显示参数和结果映射的详细信息
    org.apache.ibatis.logging.jdbc: DEBUG
    # JDBC相关的详细日志，包括结果集
    org.apache.ibatis.logging.jdbc.BaseJdbcLogger: DEBUG
    org.apache.ibatis.logging.jdbc.ConnectionLogger: DEBUG
    org.apache.ibatis.logging.jdbc.PreparedStatementLogger: DEBUG
    org.apache.ibatis.logging.jdbc.ResultSetLogger: DEBUG
    org.apache.ibatis.logging.jdbc.StatementLogger: DEBUG
    # Tomcat日志
    org.apache.catalina: INFO
    org.apache.tomcat: INFO
    # 敏感词相关日志配置 - 禁用敏感词查询的SQL日志输出
    com.kingcola.mapper.SensitiveWordMapper: OFF
    com.kingcola.implatform.service.impl.SensitiveWordServiceImpl: WARN
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID:- } --- [%t] %-40.40logger{39} : %m%n%wEx"
  file:
    name: logs/kingcola.log
    max-size: 10MB
    max-history: 20
#==============================mybatis-plus配置=========================
mybatis-plus:
  mapper-locations: classpath:/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.kingcola.entity
  global-config:
    # 数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: not_empty
      #驼峰下划线转换
      column-underline: true
      db-type: mysql
    #刷新mapper 调试神器
    refresh: true
  # 原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    # 改为使用SLF4J实现，这样可以通过日志级别精确控制敏感词日志输出
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    # 开启详细的SQL日志输出，包括查询结果
    log-prefix: "[MyBatis] "
    # 设置调用深度，显示更详细的调用栈信息
    call-setters-on-nulls: true
    # 开启返回实例映射的详细日志
    return-instance-for-empty-row: false
    # 开启详细的SQL执行日志，包括结果集
    jdbc-type-for-null: OTHER

#==============================knife4j配置=======================================
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: "default"
      paths-to-match: "/**"
      packages-to-scan: com.kingcola.controller

#==============================安全配置=======================================
# 密码加密配置
# 综合运用了Argon2强哈希算法、自动加盐（Salt）、高计算成本配置、
# 严格的密码策略以及全局胡椒（Pepper），确保密码安全性和兼容性。
# 全局胡椒（Pepper）：这是一个更深层次的防御措施。胡椒是一个全局的、保密的字符串，它被添加到所有用户的密码中，然后才进行哈希计算。
# 与盐不同，胡椒绝对不会存储在数据库里，而是作为应用的一部分，保存在配置文件或服务器环境变量中。
# 这意味着即使攻击者获取了数据库中的哈希值，他们也无法通过彩虹表进行破解，因为缺少胡椒。
# 这种设计确保了即使密码被破解，也无法通过彩虹表进行破解。
# 全局胡椒的值需要保密，不能泄露给任何人。
# 全局胡椒的值需要定期更换，以确保密码的安全性。
# 全局胡椒的值需要存储在安全的地方，不能泄露给任何人。
security:
  password:
    encoder:
      argon2:
        # salt长度
        salt-length: 16
        # hash长度
        hash-length: 32
        # 并行度
        # parallelism: 4
        # 并行度（降低以适应4核4G的服务器）
        parallelism: 2
        # 内存（从64MB降至16MB以减轻内存压力）
        # memory: 65536 # 64MB
        memory: 16384 # 16MB
        # iterations: 10
        # 迭代次数（略微降低以提升性能）
        iterations: 6
        # 全局胡椒
        pepper: "a-long-secret-pepper-string-1234567890-kingcola-icg-blog-20250630"
      policy:
        min-length: 8
        max-length: 20
        require-uppercase: true
        require-lowercase: true
        require-digit: true
        require-special-char: true
        pwned-check:
          # 是否开启密码泄露检查
          enabled: true
  login:
    failure-lock:
      # 是否开启登录失败锁定
      enabled: true
      # 最大重试次数
      max-retries: 5
      # 锁定时间（秒）
      lockout-seconds: 900 # 15分钟
totp:
  # 2FA颁发者名称
  issuer: Kingcola-ICG Blog
  # MFA密钥加密的秘钥，必须是16, 24, 或 32位
  encryption-key: "ThisIsASecretKeyForMfaEncrypt123"
