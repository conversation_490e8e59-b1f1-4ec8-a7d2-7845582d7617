package com.kingcola.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kingcola.common.Constants;
import com.kingcola.common.RedisConstants;
import com.kingcola.config.properties.*;
import com.kingcola.dto.*;
import com.kingcola.dto.user.LoginUserInfo;
import com.kingcola.entity.SysConfig;
import com.kingcola.entity.SysRole;
import com.kingcola.entity.SysUser;
import com.kingcola.enums.LoginTypeEnum;
import com.kingcola.enums.MenuTypeEnum;
import com.kingcola.exception.ServiceException;
import com.kingcola.mapper.SysConfigMapper;
import com.kingcola.mapper.SysMenuMapper;
import com.kingcola.mapper.SysRoleMapper;
import com.kingcola.mapper.SysUserMapper;
import com.kingcola.service.*;
import com.kingcola.utils.*;
import com.kingcola.implatform.service.FriendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.*;
import me.zhyd.oauth.utils.AuthStateUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.kingcola.enums.IMTerminalType;

import javax.mail.MessagingException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final SysUserMapper userMapper;

    private final SysRoleMapper roleMapper;

    private final SysMenuMapper menuMapper;

    private final EmailUtil emailUtil;

    private final RedisUtil redisUtil;

    private final PasswordEncoder passwordEncoder;

    private final PasswordPolicyValidator passwordPolicyValidator;

    private final PasswordEncoderProperties passwordEncoderProperties;

    private final LoginAttemptService loginAttemptService;

    private final PwnedPasswordService pwnedPasswordService;

    private final TwoFactorAuthService twoFactorAuthService;

    private final MfaEncryptionService mfaEncryptionService;

    private final String[] avatarList = {
            // KINGCOLA
            "https://kingcola-blog.oss-cn-hangzhou.aliyuncs.com/blog-images/pre-image/67e3f4de549ca.png",
            "https://kingcola-blog.oss-cn-hangzhou.aliyuncs.com/blog-images/pre-image/67e3f4e0f02bd.png",
            "https://kingcola-blog.oss-cn-hangzhou.aliyuncs.com/blog-images/pre-image/67e3f4e213dd3.png",
            "https://kingcola-blog.oss-cn-hangzhou.aliyuncs.com/blog-images/pre-image/67e3f4e49466b.png",
            "https://kingcola-blog.oss-cn-hangzhou.aliyuncs.com/blog-images/pre-image/67e3f518ce982.png",
            "https://kingcola-blog.oss-cn-hangzhou.aliyuncs.com/blog-images/pre-image/67e3f51999ec6.png",
            "https://kingcola-blog.oss-cn-hangzhou.aliyuncs.com/blog-images/pre-image/67e3f72db8301.png",
            "https://kingcola-blog.oss-cn-hangzhou.aliyuncs.com/blog-images/pre-image/67e3fa6c6286b.png",
            "https://kingcola-blog.oss-cn-hangzhou.aliyuncs.com/blog-images/pre-image/67e3fa6d18efc.png",
            "https://kingcola-blog.oss-cn-hangzhou.aliyuncs.com/blog-images/pre-image/6c02eca78e19ddd4bf9a945bd3371ae5.png"
    };

    private final GiteeConfigProperties giteeConfigProperties;

    private final GithubConfigProperties githubConfigProperties;

    private final QqConfigProperties qqConfigProperties;

    private final WeiboConfigProperties weiboConfigProperties;

    private final WechatProperties wechatProperties;

    private final SysConfigMapper sysConfigMapper;

    private final Pattern pattern = Pattern.compile("(?i)^KINGCOLA\\d{4}$");

    // IM好友服务，用于为新用户添加默认好友
    private final FriendService imFriendService;

    @Override
    public LoginUserInfo login(LoginDTO loginDTO, HttpServletRequest request) {
        log.info("用户 {} 尝试登录", loginDTO.getUsername());

        if (loginAttemptService.isBlocked(loginDTO.getUsername())) {
            log.warn("用户 {} 因尝试次数过多而被锁定", loginDTO.getUsername());
            throw new ServiceException("尝试次数过多，请稍后再试");
        }

        SysConfig verifySwitch = sysConfigMapper
                .selectOne(new LambdaQueryWrapper<SysConfig>().eq(SysConfig::getConfigKey, "slider_verify_switch"));
        if (verifySwitch != null && verifySwitch.getConfigValue().equals("Y")) {
            // 校验验证码
            CaptchaUtil.checkImageCode(loginDTO.getNonceStr(), loginDTO.getValue());
        }

        // 查询用户
        SysUser user = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUsername, loginDTO.getUsername())
                .or()
                .eq(SysUser::getNickname, loginDTO.getUsername()));

        // 校验是否能够登录
        validateLogin(loginDTO, user, request);
        log.info("用户 {} 密码验证通过", loginDTO.getUsername());

        // 检查是否为管理员，管理员跳过所有验证（设备验证和2FA验证），方便管理员快速登录
        List<String> roles = roleMapper.selectRolesCodeByUserId(user.getId());
        if (roles.contains(Constants.ADMIN)) {
            log.info("管理员 {} 登录，跳过设备验证和2FA验证", user.getUsername());

            // 管理员直接登录成功，跳过所有二次验证
            StpUtil.login(user.getId(), String.valueOf(getTerminalType(request)));
            String tokenValue = StpUtil.getTokenValue();
            loginAttemptService.loginSucceeded(loginDTO.getUsername());

            LoginUserInfo loginUserInfo = BeanCopyUtil.copyObj(user, LoginUserInfo.class);
            loginUserInfo.setToken(tokenValue);
            StpUtil.getSession().set(Constants.CURRENT_USER, loginUserInfo);

            // 为老用户检查并补充默认好友关系
            try {
                imFriendService.checkAndAddDefaultFriendsForExistingUser(user.getId().longValue());
            } catch (Exception e) {
                log.error("为用户 {} 检查默认好友失败，但不影响登录", user.getId(), e);
            }

            log.info("管理员 {} 仅凭账号密码登录成功", user.getUsername());
            return loginUserInfo;
        }

        String deviceFingerprint = generateDeviceFingerprint(user.getId(), request.getHeader("User-Agent"));
        boolean isDeviceTrusted = isDeviceTrusted(user.getId(), deviceFingerprint);

        String preAuthToken = UUID.randomUUID().toString();

        if (!isDeviceTrusted) {
            log.info("用户 {} 从新设备登录，需要进行设备验证。设备指纹: {}", user.getUsername(), deviceFingerprint);
            String verificationCode = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
            // 注意：这里的key是给邮件验证码用的，不是preAuthToken的key
            String verificationKey = RedisConstants.DEVICE_VERIFICATION_CODE + verificationCode;
            // 将 username 和设备指纹存入，供后续验证
            redisUtil.set(verificationKey, user.getUsername() + ":" + deviceFingerprint, 10, TimeUnit.MINUTES);

            try {
                String subject = "安全提醒：检测到新设备登录尝试";
                String content = "您好，我们检测到您的账户正在一个新设备上尝试登录。如果这是您本人操作，请使用以下验证码完成登录： " + verificationCode
                        + "。该验证码10分钟内有效。";
                emailUtil.sendSecurityNotification(user.getEmail(), subject, content);
                log.info("已向用户 {} 的邮箱 {} 发送设备验证码", user.getUsername(), user.getEmail());
            } catch (MessagingException e) {
                log.error("向用户 {} 发送设备验证邮件失败", user.getUsername(), e);
                throw new ServiceException("发送设备验证邮件失败，请稍后再试");
            }

            // 使用preAuthToken作为需要二次验证的凭证
            redisUtil.set(RedisConstants.PRE_AUTH_TOKEN + preAuthToken, user.getId(), 5, TimeUnit.MINUTES);
            LoginUserInfo response = new LoginUserInfo();
            response.setDeviceVerificationRequired(true);
            response.setPreAuthToken(preAuthToken); // 返回 preAuthToken
            // 对邮箱进行脱敏处理
            String email = user.getEmail();
            if (email != null) {
                // 使用增强的脱敏工具方法
                response.setEmail(CommonUtils.maskEmail(email));
            }
            log.info("用户 {} 需要设备验证，返回 preAuthToken 和脱敏后的邮箱", user.getUsername());
            // 返回给前端一个包含 preAuthToken 的响应
            // throw new ServiceException(602, "需要新设备验证", response);
            return response;
        }

        // 如果用户启用了2FA
        if (user.isMfaEnabled()) {
            log.info("用户 {} 已启用MFA，需要进行2FA验证", user.getUsername());
            redisUtil.set(RedisConstants.PRE_AUTH_TOKEN + preAuthToken, user.getId(), 5, TimeUnit.MINUTES);
            LoginUserInfo partialInfo = new LoginUserInfo();
            partialInfo.setMfaEnabled(true);
            partialInfo.setPreAuthToken(preAuthToken); // 返回 preAuthToken
            log.info("用户 {} 需要MFA验证，返回 preAuthToken", user.getUsername());
            // throw new ServiceException(601, "需要MFA验证", partialInfo);
            return partialInfo;
        }

        // 执行登录
        log.info("用户 {} 登录成功", user.getUsername());
        StpUtil.login(user.getId(), String.valueOf(getTerminalType(request)));
        String tokenValue = StpUtil.getTokenValue();

        loginAttemptService.loginSucceeded(loginDTO.getUsername());
        // 返回用户信息
        LoginUserInfo loginUserInfo = BeanCopyUtil.copyObj(user, LoginUserInfo.class);
        loginUserInfo.setToken(tokenValue);

        StpUtil.getSession().set(Constants.CURRENT_USER, loginUserInfo);
        return loginUserInfo;
    }

    @Override
    public LoginUserInfo loginWithMfa(MfaLoginRequest mfaLoginRequest) {
        String preAuthTokenKey = RedisConstants.PRE_AUTH_TOKEN + mfaLoginRequest.getPreAuthToken();
        Object userIdObj = redisUtil.get(preAuthTokenKey);
        if (userIdObj == null) {
            log.warn("MFA登录失败：preAuthToken '{}' 无效或已过期", mfaLoginRequest.getPreAuthToken());
            throw new ServiceException("认证已过期，请重新登录");
        }
        Integer userId = (Integer) userIdObj;
        SysUser user = userMapper.selectById(userId);
        log.info("用户 {} 正在进行MFA验证", user.getUsername());

        if (loginAttemptService.isBlocked(user.getUsername())) {
            log.warn("用户 {} 因尝试次数过多而被锁定，无法进行MFA验证", user.getUsername());
            throw new ServiceException("尝试次数过多，请稍后再试");
        }

        if (!user.isMfaEnabled()) {
            log.error("用户 {} 未启用MFA，但尝试进行MFA登录", user.getUsername());
            throw new ServiceException("2FA 验证失败");
        }

        String decryptedSecret = mfaEncryptionService.decrypt(user.getMfaSecret());
        if (!twoFactorAuthService.isCodeValid(decryptedSecret, mfaLoginRequest.getCode())) {
            loginAttemptService.loginFailed(user.getUsername());
            log.warn("用户 {} 的MFA验证码错误", user.getUsername());
            throw new ServiceException("无效的验证码");
        }

        // 检查是否有待信任的设备
        String pendingFingerprintKey = RedisConstants.PENDING_DEVICE_FINGERPRINT + mfaLoginRequest.getPreAuthToken();
        Object fingerprintObj = redisUtil.get(pendingFingerprintKey);
        if (fingerprintObj != null) {
            trustDevice(user.getId(), (String) fingerprintObj);
            log.info("用户 {} 的新设备已在MFA验证后被信任", user.getUsername());
            redisUtil.delete(pendingFingerprintKey);
        }

        // 验证成功后，立即删除令牌
        redisUtil.delete(preAuthTokenKey);
        log.info("用户 {} MFA验证成功，删除preAuthToken", user.getUsername());

        // 验证成功，执行登录
        StpUtil.login(user.getId());
        String tokenValue = StpUtil.getTokenValue();
        loginAttemptService.loginSucceeded(user.getUsername());
        log.info("用户 {} 通过MFA验证后登录成功", user.getUsername());

        LoginUserInfo loginUserInfo = BeanCopyUtil.copyObj(user, LoginUserInfo.class);
        loginUserInfo.setToken(tokenValue);
        loginUserInfo.setMfaEnabled(true);
        StpUtil.getSession().set(Constants.CURRENT_USER, loginUserInfo);

        // 为老用户检查并补充默认好友关系
        try {
            imFriendService.checkAndAddDefaultFriendsForExistingUser(user.getId().longValue());
        } catch (Exception e) {
            log.error("为用户 {} 检查默认好友失败，但不影响登录", user.getId(), e);
        }

        return loginUserInfo;
    }

    @Override
    public LoginUserInfo verifyDevice(DeviceVerificationRequest request) {
        String preAuthTokenKey = RedisConstants.PRE_AUTH_TOKEN + request.getPreAuthToken();
        Object userIdObj = redisUtil.get(preAuthTokenKey);

        if (userIdObj == null) {
            log.warn("设备验证失败：preAuthToken '{}' 无效或已过期", request.getPreAuthToken());
            throw new ServiceException("认证已过期，请重新登录");
        }
        Integer userId = (Integer) userIdObj;
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            log.error("设备验证失败：找不到userId '{}' 对应的用户", userId);
            throw new ServiceException("用户不存在");
        }
        log.info("用户 {} 正在进行新设备验证", user.getUsername());

        String verificationKey = RedisConstants.DEVICE_VERIFICATION_CODE + request.getCode();
        Object cachedData = redisUtil.get(verificationKey);

        if (cachedData == null) {
            log.warn("用户 {} 的设备验证码 '{}' 无效或已过期", user.getUsername(), request.getCode());
            throw new ServiceException("验证码无效或已过期");
        }

        String[] parts = cachedData.toString().split(":", 2);
        String usernameFromCache = parts[0];
        String deviceFingerprint = parts[1];

        if (!usernameFromCache.equals(user.getUsername())) {
            log.warn("用户 {} 的设备验证码错误", user.getUsername());
            throw new ServiceException("验证码错误");
        }
        log.info("用户 {} 的设备验证码正确", user.getUsername());

        // trustDevice(user.getId(), deviceFingerprint);
        redisUtil.delete(verificationKey);
        // log.info("用户 {} 的新设备已信任，设备指纹: {}", user.getUsername(), deviceFingerprint);

        // 如果用户启用了2FA，则需要进行2FA验证
        if (user.isMfaEnabled()) {
            // 设备验证成功，但还需要MFA，暂存设备指纹，待MFA完成后再信任
            log.info("用户 {} 设备验证成功，但仍需进行MFA验证。暂存设备指纹: {}", user.getUsername(), deviceFingerprint);
            String pendingFingerprintKey = RedisConstants.PENDING_DEVICE_FINGERPRINT + request.getPreAuthToken();
            redisUtil.set(pendingFingerprintKey, deviceFingerprint, 5, TimeUnit.MINUTES);

            LoginUserInfo partialInfo = new LoginUserInfo();
            partialInfo.setMfaEnabled(true);
            partialInfo.setPreAuthToken(request.getPreAuthToken()); // 继续使用同一个令牌
            return partialInfo;
        }

        // 设备验证成功且无需MFA，则信任设备、登录并删除preAuthToken
        trustDevice(user.getId(), deviceFingerprint);
        log.info("用户 {} 的新设备已信任，设备指纹: {}", user.getUsername(), deviceFingerprint);
        redisUtil.delete(preAuthTokenKey);
        log.info("用户 {} 设备验证成功且无需MFA，登录成功", user.getUsername());
        StpUtil.login(user.getId());
        String tokenValue = StpUtil.getTokenValue();
        loginAttemptService.loginSucceeded(user.getUsername());

        LoginUserInfo loginUserInfo = BeanCopyUtil.copyObj(user, LoginUserInfo.class);
        loginUserInfo.setToken(tokenValue);
        StpUtil.getSession().set(Constants.CURRENT_USER, loginUserInfo);

        // 为老用户检查并补充默认好友关系
        try {
            imFriendService.checkAndAddDefaultFriendsForExistingUser(user.getId().longValue());
        } catch (Exception e) {
            log.error("为用户 {} 检查默认好友失败，但不影响登录", user.getId(), e);
        }

        return loginUserInfo;
    }

    private void validateLogin(LoginDTO loginDTO, SysUser user, HttpServletRequest request) {
        if (user == null) {
            loginAttemptService.loginFailed(loginDTO.getUsername());
            throw new ServiceException("用户名或密码错误");
        }
        boolean passwordMatches;
        boolean needsUpdate = false;
        // 检查密码是否为BCrypt格式
        if (user.getPassword() != null && user.getPassword().startsWith("$2a$")) {
            passwordMatches = BCrypt.checkpw(loginDTO.getPassword(), user.getPassword());
            if (passwordMatches) {
                // 如果是旧密码格式且验证成功，使用新编码器和胡椒重新编码并更新密码
                user.setPassword(
                        passwordEncoder.encode(loginDTO.getPassword() + passwordEncoderProperties.getPepper()));
                needsUpdate = true;
            }
        } else {
            // 优先使用新方式（加胡椒）验证
            passwordMatches = passwordEncoder.matches(loginDTO.getPassword() + passwordEncoderProperties.getPepper(),
                    user.getPassword());
            // 如果验证失败，则尝试使用旧方式（不加胡椒）进行兼容验证
            if (!passwordMatches) {
                passwordMatches = passwordEncoder.matches(loginDTO.getPassword(), user.getPassword());
                // 如果旧方式验证成功，则立即升级密码
                if (passwordMatches) {
                    log.info("用户 {} 使用了未加胡椒的密码，将为其自动升级。", user.getUsername());
                    user.setPassword(
                            passwordEncoder.encode(loginDTO.getPassword() + passwordEncoderProperties.getPepper()));
                    needsUpdate = true;
                }
            }
        }

        if (!passwordMatches) {
            loginAttemptService.loginFailed(loginDTO.getUsername());
            throw new ServiceException("用户名或密码错误");
        }

        // 检查用户是否被禁用
        if (Integer.valueOf(0).equals(user.getStatus())) {
            loginAttemptService.loginFailed(loginDTO.getUsername());
            throw new ServiceException("用户已禁用");
        }

        // 登录成功，更新登录信息
        user.setIp(IpUtil.getIpAddress(request));
        user.setOs(IpUtil.getOperatingSystem(request));
        user.setBrowser(IpUtil.getBrowser(request));
        user.setLastLoginTime(LocalDateTime.now());
        // 只有在密码升级时才需要更新密码字段
        if (needsUpdate) {
            userMapper.updateById(user);
        } else {
            // 创建一个新对象，只更新必要的字段，避免更新密码
            SysUser userToUpdate = new SysUser();
            userToUpdate.setId(user.getId());
            userToUpdate.setIp(user.getIp());
            userToUpdate.setOs(user.getOs());
            userToUpdate.setBrowser(user.getBrowser());
            userToUpdate.setLastLoginTime(user.getLastLoginTime());
            // 修复：保留用户原有的mfa_enabled状态
            userToUpdate.setMfaEnabled(user.isMfaEnabled());
            userMapper.updateById(userToUpdate);
        }
    }

    private String generateDeviceFingerprint(Integer userId, String userAgent) {
        return DigestUtil.sha256Hex(userId + userAgent);
    }

    private boolean isDeviceTrusted(Integer userId, String deviceFingerprint) {
        String key = RedisConstants.USER_TRUSTED_DEVICES + userId;
        return redisUtil.sIsMember(key, deviceFingerprint);
    }

    private void trustDevice(Integer userId, String deviceFingerprint) {
        String key = RedisConstants.USER_TRUSTED_DEVICES + userId;
        redisUtil.sAdd(key, deviceFingerprint);
    }

    private Integer getTerminalType(HttpServletRequest request) {
        String terminal = request.getHeader("X-Terminal-Type");
        if (terminal != null && !terminal.isEmpty()) {
            try {
                return Integer.parseInt(terminal);
            } catch (NumberFormatException e) {
                // 处理异常，返回默认值
                return IMTerminalType.WEB.code();
            }
        }
        return IMTerminalType.WEB.code();
    }

    @Override
    public LoginUserInfo getLoginUserInfo(String source) {
        // 获取当前登录用户ID
        Integer userId = StpUtil.getLoginIdAsInt();
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        LoginUserInfo loginUserInfo = BeanCopyUtil.copyObj(user, LoginUserInfo.class);

        // 获取菜单权限列表
        if (source.equalsIgnoreCase(Constants.ADMIN)) {
            List<String> permissions;
            List<String> roles = roleMapper.selectRolesCodeByUserId(userId);
            if (roles.contains(Constants.ADMIN)) {
                permissions = menuMapper.getPermissionList(MenuTypeEnum.BUTTON.getCode());
            } else {
                permissions = menuMapper.getPermissionListByUserId(userId, MenuTypeEnum.BUTTON.getCode());
            }
            loginUserInfo.setRoles(roles);
            loginUserInfo.setPermissions(permissions);
        }

        return loginUserInfo;
    }

    @Override
    public Boolean sendEmailCode(String email) throws MessagingException {
        return sendEmailCode(email, "register");
    }

    @Override
    public Boolean sendEmailCode(String email, String type) throws MessagingException {
        // 速率限制: 1分钟内同一邮箱和类型的请求不能超过1次
        String rateLimitKey = RedisConstants.EMAIL_CODE_RATE_LIMIT + type + ":" + email;
        long count = redisUtil.increment(rateLimitKey, 1);
        if (count == 1) {
            // 首次请求，设置1分钟过期
            redisUtil.expire(rateLimitKey, 1, TimeUnit.MINUTES);
        }
        if (count > 1) {
            log.warn("邮箱 {} 发送验证码过于频繁 (类型: {})", email, type);
            throw new ServiceException("请求过于频繁，请稍后再试");
        }

        // 前置校验，防止信息泄露
        if ("register".equals(type)) {
            if (userMapper.selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getEmail, email)) != null) {
                throw new ServiceException("该邮箱已被注册！");
            }
        } else if ("forgot".equals(type)) {
            if (userMapper.selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getEmail, email)) == null) {
                throw new ServiceException("该邮箱未注册！");
            }
        }

        emailUtil.sendCode(email, type);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean register(EmailRegisterDto dto) {
        // 校验邮箱验证码
        validateEmailCode(dto);

        // 验证密码策略
        passwordPolicyValidator.validate(dto.getPassword());

        // 检查密码是否已泄露
        pwnedPasswordService.checkPassword(dto.getPassword());

        SysUser existUser = userMapper
                .selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, dto.getEmail()));
        if (existUser != null) {
            throw new ServiceException("邮箱已被注册");
        }

        SysUser userByNickname = userMapper
                .selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getNickname, dto.getNickname()));
        if (userByNickname != null) {
            throw new ServiceException("该昵称已被使用，请更换一个");
        }

        // 获取随机头像
        String avatar = avatarList[(int) (Math.random() * avatarList.length)];
        SysUser sysUser = SysUser.builder()
                .username(dto.getEmail())
                .password(passwordEncoder.encode(dto.getPassword() + passwordEncoderProperties.getPepper()))
                .nickname(dto.getNickname())
                .email(dto.getEmail())
                .avatar(avatar)
                .status(Constants.YES)
                .build();
        userMapper.insert(sysUser);

        // 添加用户角色信息
        insertRole(sysUser);

        // 为新用户添加默认好友（AI助手和自己）
        try {
            imFriendService.addDefaultFriends(sysUser.getId().longValue());
            log.info("成功为新注册用户 {} 添加默认好友", sysUser.getId());
        } catch (Exception e) {
            log.error("为新注册用户 {} 添加默认好友失败，但不影响注册流程", sysUser.getId(), e);
        }

        redisUtil.delete(RedisConstants.CAPTCHA_CODE_KEY + dto.getEmail());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean forgot(EmailRegisterDto dto) {
        // 校验邮箱验证码
        validateEmailCode(dto);

        // 验证密码策略
        passwordPolicyValidator.validate(dto.getPassword());

        // 检查密码是否已泄露
        pwnedPasswordService.checkPassword(dto.getPassword());

        SysUser existUser = userMapper
                .selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, dto.getEmail()));
        if (existUser == null) {
            throw new ServiceException("用户不存在");
        }
        existUser.setPassword(passwordEncoder.encode(dto.getPassword() + passwordEncoderProperties.getPepper()));
        userMapper.updateById(existUser);
        redisUtil.delete(RedisConstants.CAPTCHA_CODE_KEY + dto.getEmail());

        try {
            String subject = "安全提醒：您的账户密码已重置";
            String content = "您好，我们检测到您的账户密码刚刚被重置。如果您本人操作，请忽略此邮件。如果非您本人操作，请立即联系我们。";
            emailUtil.sendSecurityNotification(existUser.getEmail(), subject, content);
        } catch (MessagingException e) {
            // 即使邮件发送失败，也不应影响主流程
        }
        return true;
    }

    @Override
    public String getWechatLoginCode() {
        // 随机获取4位数字
        // String code = "KA" + (int) ((Math.random() * 9 + 1) * 1000);
        String code = "KINGCOLA" + (int) ((Math.random() * 9 + 1) * 1000);
        redisUtil.set(RedisConstants.WX_LOGIN_USER_CODE + code, "NOT-LOGIN", RedisConstants.MINUTE_EXPIRE,
                TimeUnit.SECONDS);
        return code;
    }

    @Override
    public LoginUserInfo getWechatIsLogin(String loginCode) {
        log.info("尝试获取微信登录状态，loginCode: {}", loginCode);
        // 检查登录码是否存在
        boolean hasLoginCodeKey = redisUtil.hasKey(RedisConstants.WX_LOGIN_USER_CODE + loginCode);
        log.info("登录码是否有效: {}", hasLoginCodeKey);
        // 检查用户信息是否存在
        Object value = redisUtil.get(RedisConstants.WX_LOGIN_USER + loginCode);
        log.info("获取到的登录用户信息: {}", value);

        if (value == null) {
            log.error("未找到与登录码 {} 关联的用户信息", loginCode);
            throw new ServiceException("登录失败");
        }

        try {
            LoginUserInfo loginUserInfo = JSONUtil.toBean(JSONUtil.parseObj(value), LoginUserInfo.class);
            log.info("成功解析用户信息: {}", loginUserInfo.getNickname());

            StpUtil.login(loginUserInfo.getId());
            String token = StpUtil.getTokenValue();
            log.info("生成token: {}", token);
            loginUserInfo.setToken(token);

            return loginUserInfo;
        } catch (Exception e) {
            log.error("解析用户信息失败: {}", e.getMessage(), e);
            throw new ServiceException("用户信息解析失败: " + e.getMessage());
        }
        // LoginUserInfo loginUserInfo = JSONUtil.toBean(JSONUtil.parseObj(value),
        // LoginUserInfo.class);
        // StpUtil.login(loginUserInfo.getId());
        // loginUserInfo.setToken(StpUtil.getTokenValue());
        // return loginUserInfo;
    }

    @Override
    public String wechatLogin(WxMpXmlMessage message) {
        String code = message.getContent().toUpperCase();
        log.info("收到微信登录请求，验证码: {}", code);
        // 先判断登录码是否已过期
        Object e = redisUtil.hasKey(RedisConstants.WX_LOGIN_USER_CODE + code);
        log.info("验证码是否有效: {}", e != null);
        if (e == null) {
            log.warn("验证码 {} 已过期", code);
            return "验证码已过期";
        }
        // 验证正则匹配结果
        boolean matches = pattern.matcher(code).matches();
        log.info("验证码 {} 格式是否符合要求: {}", code, matches);

        LoginUserInfo loginUserInfo = wechatLogin(message.getFromUser());
        log.info("处理微信用户 {} 登录, 获取到用户信息: {}", message.getFromUser(), loginUserInfo.getNickname());

        // 修改redis缓存 以便监听是否已经授权成功
        String userInfoJson = JSONUtil.toJsonStr(loginUserInfo);
        log.info("存入Redis的用户信息: {}", userInfoJson);
        // redisUtil.set(RedisConstants.WX_LOGIN_USER + code,
        // JSONUtil.toJsonStr(loginUserInfo), RedisConstants.MINUTE_EXPIRE,
        // TimeUnit.SECONDS);
        redisUtil.set(RedisConstants.WX_LOGIN_USER + code, userInfoJson, RedisConstants.MINUTE_EXPIRE,
                TimeUnit.SECONDS);

        // return "身份验证成功！欢迎访问：KingCola-ICG Blog。页面正在跳转中，请稍候...如未自动跳转，请刷新验证码重试。";
        return "✅ 身份验证成功！\n" +
                "欢迎访问：KingCola-ICG Blog\n" +
                "-----------------------------------\n" +
                "页面正在跳转中，请稍候...\n" +
                "如未自动跳转，请刷新验证码重试。";
    }

    @Override
    public String renderAuth(String source) {
        AuthRequest authRequest = getAuthRequest(source);
        return authRequest.authorize(AuthStateUtils.createState());
    }

    @Override
    public void authLogin(AuthCallback callback, String source, HttpServletResponse httpServletResponse)
            throws IOException {
        AuthRequest authRequest = getAuthRequest(source);
        AuthResponse<AuthUser> response = authRequest.login(callback);

        if (response.getData() == null) {
            log.info("用户取消了 {} 第三方登录", source);
            httpServletResponse.sendRedirect("https://blog.kingcola-icg.cn/");
            return;
        }
        String result = com.alibaba.fastjson.JSONObject.toJSONString(response.getData());
        log.info("第三方登录验证结果:{}", result);

        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(result);
        Object uuid = jsonObject.get("uuid");
        // 获取用户ip信息
        String ipAddress = IpUtil.getIp();
        String ipSource = IpUtil.getIp2region(ipAddress);
        // 判断是否已注册
        SysUser user = userMapper.selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUsername, uuid));
        if (ObjectUtils.isEmpty(user)) {
            // 保存账号信息
            String nickname = source + "-" + getRandomString(6);
            while (userMapper.selectCount(new LambdaQueryWrapper<SysUser>().eq(SysUser::getNickname, nickname)) > 0) {
                nickname = source + "-" + getRandomString(6);
            }
            user = SysUser.builder()
                    .username(uuid.toString())
                    .password(passwordEncoder
                            .encode(UUID.randomUUID().toString() + passwordEncoderProperties.getPepper()))
                    .loginType(source)
                    .lastLoginTime(LocalDateTime.now())
                    .ipLocation(ipAddress)
                    .ip(ipSource)
                    .status(Constants.YES)
                    .nickname(nickname)
                    .avatar(jsonObject.get("avatar").toString())
                    .build();
            userMapper.insert(user);
            // 添加角色
            insertRole(user);

            // 为新用户添加默认好友（AI助手和自己）
            try {
                imFriendService.addDefaultFriends(user.getId().longValue());
                log.info("成功为第三方登录新用户 {} 添加默认好友", user.getId());
            } catch (Exception e) {
                log.error("为第三方登录新用户 {} 添加默认好友失败，但不影响登录流程", user.getId(), e);
            }
        } else {
            // 老用户登录，检查并补充默认好友关系
            try {
                imFriendService.checkAndAddDefaultFriendsForExistingUser(user.getId().longValue());
            } catch (Exception e) {
                log.error("为第三方登录老用户 {} 检查默认好友失败，但不影响登录", user.getId(), e);
            }
        }

        StpUtil.login(user.getId());
        httpServletResponse.sendRedirect("https://blog.kingcola-icg.cn/?token=" + StpUtil.getTokenValue());
    }

    @Override
    public LoginUserInfo appletLogin(String code) {
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + wechatProperties.getAppletAppId()
                + "&secret=" + wechatProperties.getAppletSecret() + "&js_code=" + code
                + "&grant_type=authorization_code";
        String result = HttpUtil.get(url);
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(result);
        Object openid = jsonObject.get("openid");
        if (openid == null) {
            throw new ServiceException("登录失败");
        }

        // 查询用户
        SysUser user = userMapper.selectByUsername(openid.toString());

        if (user == null) {
            String ip = IpUtil.getIp();
            String avatar = avatarList[(int) (Math.random() * avatarList.length)];
            String nickname = "applet-" + getRandomString(6);
            while (userMapper.selectCount(new LambdaQueryWrapper<SysUser>().eq(SysUser::getNickname, nickname)) > 0) {
                nickname = "applet-" + getRandomString(6);
            }
            user = SysUser.builder()
                    .username(openid.toString())
                    .password(passwordEncoder
                            .encode(UUID.randomUUID().toString() + passwordEncoderProperties.getPepper()))
                    .loginType(LoginTypeEnum.APPLET.getType())
                    .lastLoginTime(LocalDateTime.now())
                    .ipLocation(IpUtil.getIp2region(ip))
                    .ip(ip)
                    .status(Constants.YES)
                    .nickname(nickname)
                    .avatar(avatar)
                    .build();
            userMapper.insert(user);
            // 添加用户角色信息
            this.insertRole(user);
        } else {
            if (user.getStatus() == Constants.NO) {
                throw new ServiceException("账号已被禁用，请联系管理员");
            }
        }

        LoginUserInfo loginUserInfo = BeanCopyUtil.copyObj(user, LoginUserInfo.class);

        StpUtil.login(loginUserInfo.getId());
        loginUserInfo.setToken(StpUtil.getTokenValue());

        return loginUserInfo;
    }

    @Override
    public Captcha getCaptcha() {
        Captcha captcha = new Captcha();
        CaptchaUtil.getCaptcha(captcha);
        return captcha;
    }

    private void validateEmailCode(EmailRegisterDto dto) {
        Object code = redisUtil.get(RedisConstants.CAPTCHA_CODE_KEY + dto.getEmail());
        if (code == null || !code.equals(dto.getCode())) {
            throw new ServiceException("验证码已过期或输入错误");
        }
    }

    private LoginUserInfo wechatLogin(String openId) {

        SysUser user = userMapper.selectByUsername(openId);
        if (ObjectUtils.isEmpty(user)) {
            String ip = IpUtil.getIp();
            String ipSource = IpUtil.getIp2region(ip);

            String nickname = "WECHAT-" + getRandomString(6);
            while (userMapper.selectCount(new LambdaQueryWrapper<SysUser>().eq(SysUser::getNickname, nickname)) > 0) {
                nickname = "WECHAT-" + getRandomString(6);
            }
            // 保存账号信息
            user = SysUser.builder()
                    .username(openId)
                    .password(passwordEncoder.encode(openId + passwordEncoderProperties.getPepper()))
                    .nickname(nickname)
                    .avatar(avatarList[(int) (Math.random() * avatarList.length)])
                    .loginType(LoginTypeEnum.WECHAT.getType())
                    .lastLoginTime(LocalDateTime.now())
                    .ip(ip)
                    .ipLocation(ipSource)
                    .status(Constants.YES)
                    .build();
            userMapper.insert(user);

            // 添加用户角色信息
            this.insertRole(user);

            // 为新用户添加默认好友（AI助手和自己）
            try {
                imFriendService.addDefaultFriends(user.getId().longValue());
                log.info("成功为微信登录新用户 {} 添加默认好友", user.getId());
            } catch (Exception e) {
                log.error("为微信登录新用户 {} 添加默认好友失败，但不影响登录流程", user.getId(), e);
            }
        } else {
            // 老用户登录，检查并补充默认好友关系
            try {
                imFriendService.checkAndAddDefaultFriendsForExistingUser(user.getId().longValue());
            } catch (Exception e) {
                log.error("为微信登录老用户 {} 检查默认好友失败，但不影响登录", user.getId(), e);
            }
        }

        return BeanCopyUtil.copyObj(user, LoginUserInfo.class);
    }

    /**
     * 添加用户角色信息
     *
     * @param user
     */
    private void insertRole(SysUser user) {
        SysRole sysRole = roleMapper
                .selectOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getCode, Constants.USER));
        roleMapper.addRoleUser(user.getId(), Collections.singletonList(sysRole.getId()));
    }

    /**
     * 随机生成6位数的字符串
     */
    public static String getRandomString(int length) {
        String str = "QWERTYUIOPASDFGHJKLZXCVBNMqwertyuiopasdfghjklzxcvbnm0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(str.length());
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }

    private @NotNull AuthRequest getAuthRequest(String source) {
        AuthRequest authRequest = null;
        switch (source) {
            case "gitee":
                authRequest = new AuthGiteeRequest(AuthConfig.builder()
                        .clientId(giteeConfigProperties.getAppId())
                        .clientSecret(giteeConfigProperties.getAppSecret())
                        .redirectUri(giteeConfigProperties.getRedirectUrl())
                        .build());
                break;
            case "qq":
                authRequest = new AuthQqRequest(AuthConfig.builder()
                        .clientId(qqConfigProperties.getAppId())
                        .clientSecret(qqConfigProperties.getAppSecret())
                        .redirectUri(qqConfigProperties.getRedirectUrl())
                        .build());
                break;
            case "weibo":
                authRequest = new AuthWeiboRequest(AuthConfig.builder()
                        .clientId(weiboConfigProperties.getAppId())
                        .clientSecret(weiboConfigProperties.getAppSecret())
                        .redirectUri(weiboConfigProperties.getRedirectUrl())
                        .build());
                break;
            case "github":
                authRequest = new AuthGithubRequest(AuthConfig.builder()
                        .clientId(githubConfigProperties.getAppId())
                        .clientSecret(githubConfigProperties.getAppSecret())
                        .redirectUri(githubConfigProperties.getRedirectUrl())
                        .build());
                break;
            default:
                break;
        }
        if (null == authRequest) {
            throw new AuthException("授权地址无效");
        }
        return authRequest;
    }

}
