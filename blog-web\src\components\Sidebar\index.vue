<template>
  <aside class="sidebar">
    <el-card class="author-card">
      <div class="floating-cards">
        <span v-for="n in 12" :key="n" class="card-particle"></span>
      </div>
      <div class="card-header">
        <div class="author-avatar">
          <el-avatar class="avatar" :src="$store.state.webSiteInfo.authorAvatar" alt="作者头像" />
        </div>
        <div class="status-badge">
          <span><i class="fas fa-circle"></i>在线</span>
        </div>
      </div>
      <div class="author-info">
        <div class="star-particles">
          <i class="fas fa-star star" v-for="n in 8" :key="n"></i>
        </div>
        <div class="title-wrapper">
          <span class="decorator left"><i class="fas fa-crown"></i></span>
          <h3 class="flowing-text">
            <span class="title-en" :data-text="getTitleParts($store.state.webSiteInfo.author)[0]">
              {{ getTitleParts($store.state.webSiteInfo.author)[0] }}
            </span>
            <span class="title-divider">
              <i class="fas fa-star"></i>
            </span>
            <span class="title-cn" :data-text="getTitleParts($store.state.webSiteInfo.author)[1]">
              {{ getTitleParts($store.state.webSiteInfo.author)[1] }}
            </span>
          </h3>
          <span class="decorator right"><i class="fas fa-crown"></i></span>
        </div>
        <p class="bio">{{ $store.state.webSiteInfo.authorInfo }}</p>
      </div>
      <div class="social-links">
        <div v-for="item in socialLinksWithData" :key="item.type">
          <el-tooltip placement="top" :content="item.content">
            <a v-if="$store.state.webSiteInfo.showList.indexOf(item.type) !== -1" href="javascript:void(0)"
              :title="item.title" :class="`social-btn ${item.type}`" @click="copyToClipboard(item)">
              <i :class="item.icon"></i>
            </a>
          </el-tooltip>
        </div>
      </div>
    </el-card>

    <el-card v-if="announcements.length" class="section announcement glass-card">
      <h3>
        <i class="fas fa-bullhorn"></i>
        公告
      </h3>
      <div class="announcement-content">
        <div class="announcement-item" v-for="(item, index) in announcements" :key="index">
          <span v-html="item.content"></span>
        </div>
      </div>
    </el-card>

    <el-card class="section glass-card">
      <h3>
        <i class="fas fa-star"></i>
        个性化推荐
        <el-tooltip content="刷新推荐" placement="top">
          <i class="fas fa-sync-alt refresh-icon" :class="{ 'is-loading': isLoading }"
            @click="refreshRecommendations"></i>
        </el-tooltip>
      </h3>

      <div v-if="isLoading" class="skeleton-loading">
        <div class="skeleton-item" v-for="n in 3" :key="n">
          <div class="skeleton-image"></div>
          <div class="skeleton-content">
            <div class="skeleton-title"></div>
            <div class="skeleton-date"></div>
          </div>
        </div>
      </div>

      <div v-else-if="hot.length > 0" class="post-list">
        <transition-group name="post-list">
          <router-link v-for="post in hot" :key="post.id" :to="`/post/${post.id}`" class="post-item">
            <div class="post-image">
              <img v-lazy="post.cover" :key="post.cover" :alt="post.title">
            </div>
            <div class="post-meta">
              <h4>{{ post.title }}</h4>
              <time>
                <i class="far fa-calendar-alt"></i>
                {{ formatTime(post.createTime) }}
              </time>
            </div>
          </router-link>
        </transition-group>
      </div>

      <div v-else class="no-posts">
        <i class="fas fa-inbox"></i>
        <p>暂无推荐文章</p>
      </div>
    </el-card>

    <el-card class="section contribution-section glass-card">
      <h3>
        <i class="fas fa-crown"></i>
        博客贡献榜
      </h3>
      <ContributionRank />
    </el-card>

    <el-card class="section glass-card">
      <h3>
        <i class="fas fa-tags"></i>
        标签云
      </h3>
      <Tag />
    </el-card>
  </aside>
</template>

<script>
import { getPersonalizedRecommendsApi } from '@/api/article'
import Tag from './components/tagCloud.vue'
import ContributionRank from './components/ContributionRank.vue'

export default {
  name: 'Sidebar',
  components: {
    Tag,
    ContributionRank
  },
  data() {
    return {
      hot: [],
      announcements: [],
      socialLinks: [
        {
          icon: 'fab fa-github',
          type: 'github',
          content: '点击跳转GitHub主页',
          icCopy: false
        },
        {
          icon: 'fab fa-git-alt',
          type: 'gitee',
          content: '点击跳转GitEE主页',
          icCopy: false
        },
        {
          icon: 'fab fa-qq',
          title: 'QQ',
          type: 'qq',
          content: '点击复制QQ号',
          icCopy: true
        },
        {
          icon: 'fas fa-users',
          title: 'QQ群',
          type: 'qqGroup',
          content: '点击复制QQ群号',
          icCopy: true
        },
        {
          icon: 'fas fa-at',
          title: '邮箱',
          type: 'email',
          content: '点击复制邮箱',
          icCopy: true
        },
        {
          icon: 'fab fa-weixin',
          title: '微信',
          type: 'wechat',
          content: '点击复制微信号',
          icCopy: true
        }
      ],
      tags: [],
      isLoading: true,
      hoverStates: {
        card: false
      }
    }
  },
  computed: {
    socialLinksWithData() {
      return this.socialLinks.map(item => {
        const linkMap = {
          github: this.$store.state.webSiteInfo.github,
          gitee: this.$store.state.webSiteInfo.gitee,
          qq: this.$store.state.webSiteInfo.qqNumber,
          qqGroup: this.$store.state.webSiteInfo.qqGroup,
          email: this.$store.state.webSiteInfo.email,
          wechat: this.$store.state.webSiteInfo.wechat
        }
        return {
          ...item,
          link: linkMap[item.type]
        }
      })
    }
  },
  watch: {
    '$store.state.notice'(val) {
      if (val && val.right) {
        this.announcements = val.right
        this.animateAnnouncements()
      }
    }
  },
  mounted() {
    this.fetchData()
    // 添加鼠标移动效果
    this.initMouseMoveEffect()
    // 初始化公告数据
    if (this.$store.state.notice && this.$store.state.notice.right) {
      this.announcements = this.$store.state.notice.right
      this.animateAnnouncements()
    }
  },
  methods: {
    /**
     * 获取数据
     */
    async fetchData() {
      this.isLoading = true
      try {
        const res = await getPersonalizedRecommendsApi()
        this.hot = res.data
        // 添加延迟加载动画
        setTimeout(() => {
          this.isLoading = false
          // 添加列表项动画
          this.animatePostItems()
        }, 600)
      } catch (error) {
        console.error('获取推荐文章失败:', error)
        this.$message.error('加载推荐文章失败，请稍后再试')
        this.isLoading = false
      }
    },
    /**
     * 复制到剪贴板
     */
    copyToClipboard(item) {
      if (item.icCopy) {
        navigator.clipboard.writeText(item.link).then(() => {
          this.$message({
            message: `${item.title}账号已复制到剪贴板`,
            type: 'success',
            duration: 2000,
            showClose: true,
            offset: 80
          })
        }).catch(() => {
          this.$message.error('复制失败，请手动复制')
        })
      } else {
        window.open(item.link, '_blank')
      }
    },
    /**
     * 初始化鼠标移动效果
     */
    initMouseMoveEffect() {
      const authorCard = document.querySelector('.author-card')
      if (!authorCard) return

      authorCard.addEventListener('mousemove', (e) => {
        const rect = authorCard.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top
        const centerX = rect.width / 2
        const centerY = rect.height / 2

        const moveX = (x - centerX) / 20
        const moveY = (y - centerY) / 20

        authorCard.style.transform = `translateY(-4px) perspective(1000px) rotateX(${-moveY}deg) rotateY(${moveX}deg)`
      })

      authorCard.addEventListener('mouseleave', () => {
        authorCard.style.transform = 'translateY(-4px) perspective(1000px) rotateX(0deg) rotateY(0deg)'
      })
    },
    /**
     * 为公告项添加动画
     */
    animateAnnouncements() {
      if (!this.announcements.length) return

      setTimeout(() => {
        const items = document.querySelectorAll('.announcement-item')
        items.forEach((item, index) => {
          item.style.opacity = '0'
          item.style.transform = 'translateY(20px)'
          setTimeout(() => {
            item.style.transition = 'all 0.5s ease'
            item.style.opacity = '1'
            item.style.transform = 'translateY(0)'
          }, 100 * index)
        })
      }, 300)
    },
    /**
     * 为文章列表添加动画
     */
    animatePostItems() {
      setTimeout(() => {
        const items = document.querySelectorAll('.post-item')
        items.forEach((item, index) => {
          item.style.opacity = '0'
          item.style.transform = 'translateX(20px)'
          setTimeout(() => {
            item.style.transition = 'all 0.5s ease'
            item.style.opacity = '1'
            item.style.transform = 'translateX(0)'
          }, 100 * index)
        })
      }, 300)
    },
    /**
     * 刷新推荐文章
     */
    refreshRecommendations() {
      if (this.isLoading) return
      this.fetchData()
      // 旋转刷新图标动画
      const refreshIcon = document.querySelector('.refresh-icon')
      refreshIcon.classList.add('rotating')
      setTimeout(() => {
        refreshIcon.classList.remove('rotating')
      }, 1000)
    },
    /**
     * 格式化时间显示
     */
    formatTime(timeString) {
      if (!timeString) return '';

      const date = new Date(timeString);

      // 统一显示年月日格式: YYYY-MM-DD
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
    },
    /**
     * 分离博客名称中的英文和中文部分
     */
    getTitleParts(title) {
      if (!title) return ['', ''];

      // 特殊处理KingCola-ICG学生团队
      if (title.includes('KingCola-ICG')) {
        return ['KingCola-ICG', '学生团队'];
      }

      // 一般情况的正则匹配
      const match = title.match(/^([A-Za-z0-9\-]+)(.*)$/);

      if (match && match.length >= 3) {
        return [match[1], match[2]];
      }

      return [title, ''];
    }
  }
}
</script>

<style lang="scss" scoped>
@use "sass:math";
@use "sass:list";

.sidebar {
  position: sticky;
  top: 80px;
  width: 100%;
  max-width: 320px;

  .author-card {
    padding: $spacing-md;
    margin-bottom: $spacing-lg;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    position: relative;
    border-radius: 16px !important;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    }

    // 基础渐变背景层
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 120px;
      // 日间模式：紫蓝青三色渐变，营造现代科技感
      background: linear-gradient(135deg, #6366f1 0%, #3b82f6 50%, #06b6d4 100%);
      transition: opacity 0.3s ease;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
      overflow: hidden;
    }

    // 流光效果层
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 120px;
      // 流光渐变：从透明到白色再到透明
      background: linear-gradient(
        90deg,
        transparent 0%,
        transparent 40%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0.6) 55%,
        rgba(255, 255, 255, 0.3) 60%,
        transparent 70%,
        transparent 100%
      );
      // 流光动画：从左到右扫过
      animation: streamingLight 4s ease-in-out infinite;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
      transform: translateX(-100%);
    }

    // 悬停效果：背景层透明度变化
    &:hover::before {
      opacity: 0.8;
    }

    // 悬停效果：流光层加速动画
    &:hover::after {
      animation-duration: 2s;
    }

    .card-header {
      position: relative;
      margin-bottom: 20px;
      display: flex;
      justify-content: center;

      .author-avatar {
        width: 88px;
        height: 88px;
        position: relative;
        z-index: 2;

        .avatar {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: cover;
          border: 4px solid #fff;
          box-shadow: 0 8px 24px rgba(99, 102, 241, 0.25);
          transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);

          &:hover {
            transform: scale(1.08);
          }
        }
      }

      .status-badge {
        position: absolute;
        bottom: 0;
        right: 50%;
        transform: translateX(32px);
        background: linear-gradient(135deg, #10b981, #059669);
        border-radius: 12px;
        padding: 4px 12px;
        font-size: 0.75rem;
        color: white;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
        z-index: 2;

        span {
          display: flex;
          align-items: center;
          gap: 4px;

          i {
            color: white;
            font-size: 0.6rem;
            animation: pulse 1.5s infinite;
          }
        }
      }
    }

    .author-info {
      text-align: center;
      margin-bottom: 20px;
      position: relative;
      z-index: 2;

      @media (max-width: 768px) {
        h3.flowing-text {
          .title-en {
            font-size: 1.3rem;
          }

          .title-cn {
            font-size: 1.1rem;
          }
        }
      }

      .star-particles {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 40px;
        z-index: 1;
        pointer-events: none;
        overflow: hidden;
      }

      .title-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        margin-bottom: 10px;
        position: relative;
        padding: 0 10px;

        &::before,
        &::after {
          content: '';
          position: absolute;
          height: 2px;
          width: 30px;
          background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.3), transparent);
          top: 50%;
        }

        &::before {
          left: -20px;
        }

        &::after {
          right: -20px;
        }

        .decorator {
          font-size: 0.9rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fbbf24;
          animation: pulse 2s infinite;
          filter: drop-shadow(0 2px 4px rgba(251, 191, 36, 0.3));

          &.left {
            transform: rotate(-15deg);
          }

          &.right {
            transform: rotate(15deg);
          }
        }
      }

      h3 {
        font-size: 1.4rem;
        font-weight: 700;
        color: $primary;
        position: relative;
        display: inline-block;
        padding: 0;
        margin: 0;

        &.flowing-text {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 2px;
          position: relative;

          .title-divider {
            font-size: 0.7rem;
            color: #f59e0b;
            margin: 2px 0;
            opacity: 0.8;
            animation: twinkle 1.5s infinite;

            i {
              filter: drop-shadow(0 0 3px rgba(245, 158, 11, 0.5));
            }
          }

          .title-en,
          .title-cn {
            background: linear-gradient(90deg,
                #6366f1 0%,
                #8b5cf6 15%,
                #d946ef 30%,
                #ec4899 45%,
                #f43f5e 60%,
                #8b5cf6 75%,
                #6366f1 100%);
            background-size: 200% auto;
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: shine 5s linear infinite;
            position: relative;
            text-shadow: 0 0 2px rgba(99, 102, 241, 0.1);

            // 光晕效果
            &::before {
              content: attr(data-text);
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              height: 100%;
              background: inherit;
              filter: blur(8px);
              opacity: 0.3;
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              z-index: -1;
            }
          }

          .title-en {
            font-size: 1.5rem;
            font-weight: 700;
            letter-spacing: 1px;
          }

          .title-cn {
            font-size: 1.3rem;
            font-weight: 600;
            letter-spacing: 2px;
          }

          // 整体流光效果
          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 50%;
            height: 100%;
            background: linear-gradient(90deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.8) 50%,
                rgba(255, 255, 255, 0) 100%);
            transform: skew(-20deg);
            animation: shine-effect 3s ease-in-out infinite;
            z-index: 1;
            pointer-events: none;
          }
        }
      }

      .bio {
        font-size: 0.9rem;
        color: var(--text-secondary);
        line-height: 1.6;
        margin-bottom: 16px;
      }
    }

    .social-links {
      display: flex;
      justify-content: center;
      gap: 12px;
      margin-top: 20px;
      position: relative;
      z-index: 2;

      .social-btn {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12px;
        background: rgba(99, 102, 241, 0.08);
        font-size: 1.2rem;
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        position: relative;
        text-decoration: none;

        &:hover {
          transform: translateY(-4px) scale(1.1);
        }
      }

      .qq {
        color: #60a5fa;

        &:hover {
          background: #60a5fa;
          color: white;
          box-shadow: 0 6px 20px rgba(96, 165, 250, 0.4);
        }
      }

      .qqGroup {
        color: #e1c235;

        &:hover {
          background: #e1c235;
          color: white;
          box-shadow: 0 6px 20px rgba(225, 194, 53, 0.4);
        }
      }

      .github {
        color: #333;

        &:hover {
          background: #333;
          color: white;
          box-shadow: 0 6px 20px rgba(51, 51, 51, 0.3);
        }
      }

      .gitee {
        color: #ee3434;

        &:hover {
          background: #ee3434;
          color: white;
          box-shadow: 0 6px 20px rgba(238, 52, 52, 0.4);
        }
      }

      .email {
        color: #d872a7;

        &:hover {
          background: #d872a7;
          color: white;
          box-shadow: 0 6px 20px rgba(216, 114, 167, 0.4);
        }
      }

      .wechat {
        color: #10b981;

        &:hover {
          background: #10b981;
          color: white;
          box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        }
      }
    }

    .floating-cards {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;
      overflow: hidden;
      opacity: 0;
      transition: opacity 0.5s ease;
    }

    &:hover .floating-cards {
      opacity: 1;
    }

    .card-particle {
      position: absolute;
      width: 10px;
      height: 10px;
      background: white;
      border-radius: 2px;
      opacity: 0;
      animation: fall 3s ease-in-out infinite;

      @for $i from 1 through 12 {
        &:nth-child(#{$i}) {
          left: #{math.random(100)}#{"%"};
          animation-delay: #{math.random(3000)}ms;
          background: #{list.nth(
 (#6366f1, #8b5cf6, #ec4899, #10b981, #f59e0b, #ef4444),
          math.random(6))
        }

        ;
        transform: rotate(#{$i * 30}deg);
      }
    }
  }
}
}

.glass-card {
  background: rgba(255, 255, 255, 0.7) !important;
  backdrop-filter: blur(10px);
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08) !important;
  border: none !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12) !important;
  }

  @media (prefers-color-scheme: dark) {
    background: rgba(30, 30, 30, 0.8) !important;
  }
}

// 骨架屏加载效果
.skeleton-loading {
  padding: 0 0 10px;

  .skeleton-item {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding-left: 40px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 8px;
      top: 50%;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;
      background: #e6e6e6;
      border-radius: 8px;
      animation: pulse-bg 1.5s infinite;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .skeleton-image {
      width: 100px;
      height: 70px;
      background: #e6e6e6;
      border-radius: 10px;
      animation: pulse-bg 1.5s infinite;
      flex-shrink: 0;
    }

    .skeleton-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .skeleton-title {
        height: 16px;
        background: #e6e6e6;
        border-radius: 4px;
        margin-bottom: 10px;
        width: 100%;
        animation: pulse-bg 1.5s infinite;
      }

      .skeleton-date {
        height: 12px;
        background: #e6e6e6;
        border-radius: 4px;
        width: 60%;
        animation: pulse-bg 1.5s infinite 0.2s;
      }
    }
  }
}

.no-posts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  color: #9ca3af;

  i {
    font-size: 2.5rem;
    margin-bottom: 10px;
  }

  p {
    font-size: 0.9rem;
  }
}

// 刷新图标
.refresh-icon {
  margin-left: auto;
  cursor: pointer;
  font-size: 0.9rem;
  color: #9ca3af;
  transition: all 0.3s ease;

  &:hover {
    color: #6366f1;
  }

  &.rotating {
    animation: rotate 1s linear;
  }

  &.is-loading {
    animation: rotate 1s linear infinite;
    pointer-events: none;
  }
}

// 流光动画：从左到右的扫过效果
@keyframes streamingLight {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }

  10% {
    opacity: 1;
  }

  90% {
    opacity: 1;
  }

  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-bg {

  0%,
  100% {
    opacity: 0.5;
  }

  50% {
    opacity: 0.8;
  }
}

.section .post-item:has(img[lazy=loading]),
.section .post-item:has(img[lazy=error]) {
  .post-image {
    background-color: #e6e6e6;
    animation: pulse-bg 1.5s infinite;

    img {
      visibility: hidden;
    }
  }

  .post-meta {

    h4,
    time {
      color: transparent !important;
      background: #e6e6e6;
      border-radius: 4px;
      animation: pulse-bg 1.5s infinite;
    }

    h4 {
      min-height: calc(0.95rem * 1.4 * 2);
      margin-bottom: 6px;
    }

    time {
      width: 70%;
    }
  }
}

.section {
  margin-bottom: $spacing-lg;

  h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #6366f1;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 2px solid rgba(99, 102, 241, 0.1);
    display: flex;
    align-items: center;

    i {
      margin-right: 10px;
      font-size: 1.2rem;
    }
  }

  .post-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    counter-reset: post-counter;

    .post-item {
      display: flex;
      gap: 16px;
      text-decoration: none;
      transition: all 0.3s ease;
      position: relative;
      padding-left: 32px;
      border-radius: 8px;
      padding: 12px;
      padding-left: 40px;

      &:hover {
        background: rgba(99, 102, 241, 0.05);
      }

      &::before {
        content: counter(post-counter);
        counter-increment: post-counter;
        position: absolute;
        left: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 24px;
        height: 24px;
        background: var(--number-bg, #f87171);
        color: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        font-weight: 600;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
      }

      &:nth-child(2)::before {
        --number-bg: #fbbf24;
      }

      &:nth-child(3)::before {
        --number-bg: #60a5fa;
      }

      &:nth-child(n+4)::before {
        --number-bg: #9ca3af;
      }

      &:hover {
        transform: translateX(4px);

        &::before {
          transform: translateY(-50%) scale(1.15);
        }

        h4 {
          color: #6366f1;
        }
      }

      .post-image {
        position: relative;
        width: 100px;
        height: 70px;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 30%;
          background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
          z-index: 1;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s ease;

          &:hover {
            transform: scale(1.1);
          }
        }
      }

      .post-meta {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        h4 {
          font-size: 0.95rem;
          font-weight: 500;
          color: var(--text-primary);
          margin-bottom: 6px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          line-height: 1.4;
          transition: color 0.3s ease;
        }

        time {
          font-size: 0.8rem;
          color: #8b8b8b;
          display: flex;
          align-items: center;
          gap: 6px;
          background: rgba(99, 102, 241, 0.06);
          border-radius: 12px;
          padding: 2px 8px;
          width: fit-content;
          margin-top: 2px;
          transition: background 0.3s ease, color 0.3s ease;
          white-space: nowrap;
          font-family: var(--font-mono, monospace);

          i {
            font-size: 0.75rem;
            color: #6366f1;
            opacity: 0.7;
          }

          &:hover {
            background: rgba(99, 102, 241, 0.1);
          }

          &::before {
            content: none;
          }
        }
      }
    }
  }
}

// 列表动画
.post-list-enter-active,
.post-list-leave-active {
  transition: all 0.5s;
}

.post-list-enter,
.post-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.post-list-move {
  transition: transform 0.5s;
}

.announcement {
  h3 {
    i {
      margin-right: 8px;
      color: #f59e0b;
      animation: shake 1.5s ease-in-out infinite;
    }
  }

  .announcement-content {
    .announcement-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 12px;
      border-radius: 8px;
      margin-bottom: 8px;
      border-bottom: 1px dashed rgba(99, 102, 241, 0.1);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(99, 102, 241, 0.05);
      }

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      span {
        font-size: 0.9rem;
        color: var(--text-secondary);
        line-height: 1.6;
        transition: all 0.3s ease;
      }

      &:hover span {
        color: var(--text-primary);
      }
    }
  }
}

@media (prefers-color-scheme: dark) {
  .sidebar {
    // 暗色模式下的作者卡片背景
    .author-card {
      // 基础渐变背景层 - 暗色版本
      &::before {
        background: linear-gradient(135deg, #4c1d95 0%, #1e3a8a 50%, #0c4a6e 100%);
      }

      // 流光效果层 - 暗色模式下使用更亮的流光
      &::after {
        background: linear-gradient(
          90deg,
          transparent 0%,
          transparent 40%,
          rgba(255, 255, 255, 0.4) 50%,
          rgba(255, 255, 255, 0.8) 55%,
          rgba(255, 255, 255, 0.4) 60%,
          transparent 70%,
          transparent 100%
        );
      }
    }

    .section {
      .post-item {
        &::before {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        &:hover {
          background: rgba(99, 102, 241, 0.1);
        }

        .post-meta {
          time {
            color: #777;
          }
        }

        &:hover h4 {
          color: #818cf8;
        }
      }
    }

    .announcement {
      .announcement-item {
        &:hover {
          background: rgba(99, 102, 241, 0.1);
        }
      }
    }

    .skeleton-loading {
      .skeleton-item {

        &::before,
        .skeleton-image,
        .skeleton-content .skeleton-title,
        .skeleton-content .skeleton-date {
          background: #424242;
        }
      }
    }
  }
}

@include responsive(lg) {
  .sidebar {
    display: none;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-5px);
  }

  100% {
    transform: translateY(0px);
  }
}

.author-avatar .avatar {
  animation: float 4s ease-in-out infinite;
}

@keyframes fall {
  0% {
    transform: translateY(-20px) rotate(0deg);
    opacity: 0;
  }

  20% {
    opacity: 0.8;
  }

  80% {
    opacity: 0.8;
  }

  100% {
    transform: translateY(300px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes shake {
  0% {
    transform: rotate(0deg);
  }

  25% {
    transform: rotate(-10deg);
  }

  75% {
    transform: rotate(10deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.5;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.5;
  }
}

.fa-star {
  color: #ef5151;
}

.fa-tags {
  color: #e329d3;
}

.fa-crown {
  color: #fbbf24;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

.contribution-section {
  h3 {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;

    i {
      font-size: 1.2rem;
      margin-right: 0;
      background: linear-gradient(135deg, #fbbf24, #d97706);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      filter: drop-shadow(0 1px 1px rgba(251, 191, 36, 0.2));
      transform-origin: center bottom;
    }
  }

  &:hover h3 i {
    animation: bounce 1s ease;
  }
}

@keyframes bounce {

  0%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-8px);
  }

  60% {
    transform: translateY(-4px);
  }
}

@keyframes shine {
  from {
    background-position: 0% center;
  }

  to {
    background-position: 200% center;
  }
}

@keyframes float-text {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-2px);
  }

  100% {
    transform: translateY(0px);
  }
}

@keyframes shimmer {
  0% {
    width: 0;
    left: -30%;
    opacity: 0.6;
  }

  50% {
    opacity: 0.8;
  }

  100% {
    width: 30%;
    left: 110%;
    opacity: 0.6;
  }
}

.flowing-text {
  animation: shine 5s linear infinite, float-text 3s ease-in-out infinite !important;
}

// 添加星星效果
.star-particles {
  position: absolute;
  top: -10px;
  left: 0;
  right: 0;
  height: 40px;
  z-index: 1;
  pointer-events: none;
  overflow: hidden;
}

.star {
  position: absolute;
  opacity: 0;
  animation: star-anim 3s ease-in-out infinite;

  &:nth-child(1) {
    left: 20%;
    top: 30%;
    font-size: 0.5rem;
    animation-delay: 0.2s;
    color: #fbbf24;
    transform: rotate(45deg);
  }

  &:nth-child(2) {
    left: 45%;
    top: 10%;
    font-size: 0.7rem;
    animation-delay: 0.5s;
    color: #6366f1;
    transform: rotate(90deg);
  }

  &:nth-child(3) {
    left: 70%;
    top: 40%;
    font-size: 0.6rem;
    animation-delay: 1.2s;
    color: #d946ef;
    transform: rotate(135deg);
  }

  &:nth-child(4) {
    left: 85%;
    top: 20%;
    font-size: 0.5rem;
    animation-delay: 1.7s;
    color: #f59e0b;
    transform: rotate(180deg);
  }

  &:nth-child(5) {
    left: 15%;
    top: 50%;
    font-size: 0.6rem;
    animation-delay: 0.7s;
    color: #6366f1;
    transform: rotate(225deg);
  }

  &:nth-child(6) {
    left: 40%;
    top: 60%;
    font-size: 0.4rem;
    animation-delay: 1.4s;
    color: #fbbf24;
    transform: rotate(270deg);
  }

  &:nth-child(7) {
    left: 65%;
    top: 70%;
    font-size: 0.7rem;
    animation-delay: 0.9s;
    color: #d946ef;
    transform: rotate(315deg);
  }

  &:nth-child(8) {
    left: 90%;
    top: 55%;
    font-size: 0.5rem;
    animation-delay: 2.1s;
    color: #f59e0b;
    transform: rotate(360deg);
  }

  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.6));
}

@keyframes star-anim {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.5);
  }

  50% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }

  100% {
    opacity: 0;
    transform: translateY(-10px) scale(0.5);
  }
}

@keyframes shine-effect {
  0% {
    left: -100%;
    opacity: 0;
  }

  20% {
    opacity: 0;
  }

  40% {
    opacity: 0.8;
  }

  60% {
    opacity: 0;
  }

  100% {
    left: 150%;
    opacity: 0;
  }
}

@keyframes twinkle {

  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.post-image img {
  &[lazy=loading],
  &[lazy=error] {
    background-color: #e0e0e0;
    animation: pulse-bg 1.5s infinite;
  }
}
</style>