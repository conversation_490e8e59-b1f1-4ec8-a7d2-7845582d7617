package com.kingcola.implatform.controller;

import com.kingcola.common.Result;
import com.kingcola.implatform.service.AiStreamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * AI流式响应控制器
 * 负责接收HTTP请求并委托给Service层处理
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@RestController
@RequestMapping("/ai/stream")
@RequiredArgsConstructor
@Api(tags = "AI流式响应接口")
@Validated
public class AiStreamController {

    private final AiStreamService aiStreamService;

    @ApiOperation("创建AI流式对话")
    @GetMapping(value = "/chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamChat(@RequestParam String content,
                                @RequestParam String chatType,
                                @RequestParam Long receiverId,
                                @RequestParam(required = false) Long senderId,
                                @RequestParam(required = false) String senderNickname,
                                @RequestParam(required = false) String sessionId,
                                @RequestParam(required = false) String token) {

        // 委托给Service层处理业务逻辑
        return aiStreamService.createStreamChat(content, chatType, receiverId,
                                               senderId, senderNickname, sessionId, token);
    }

    @ApiOperation("获取活跃连接数")
    @GetMapping("/connections/count")
    public Result<Integer> getActiveConnectionsCount() {
        return Result.success(aiStreamService.getActiveConnectionsCount());
    }

    @ApiOperation("关闭指定会话连接")
    @PostMapping("/connections/{sessionId}/close")
    public Result<Void> closeConnection(@PathVariable String sessionId) {
        boolean success = aiStreamService.closeConnection(sessionId);
        if (success) {
            return Result.success();
        } else {
            return Result.error("关闭连接失败或连接不存在");
        }
    }
}
