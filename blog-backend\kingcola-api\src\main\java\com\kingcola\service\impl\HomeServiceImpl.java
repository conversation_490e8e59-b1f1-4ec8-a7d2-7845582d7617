package com.kingcola.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kingcola.common.Constants;
import com.kingcola.common.RedisConstants;
import com.kingcola.common.Result;
import com.kingcola.entity.SysArticle;
import com.kingcola.entity.SysNotice;
import com.kingcola.entity.SysUser;
import com.kingcola.entity.SysWebConfig;
import com.kingcola.mapper.SysArticleMapper;
import com.kingcola.mapper.SysNoticeMapper;
import com.kingcola.mapper.SysUserMapper;
import com.kingcola.mapper.SysWebConfigMapper;
import com.kingcola.service.HomeService;
import com.kingcola.utils.IpUtil;
import com.kingcola.utils.RedisUtil;
import java.security.MessageDigest;
import java.nio.charset.StandardCharsets;
import com.kingcola.vo.user.ContributionRankVO;
import eu.bitwalker.useragentutils.Browser;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class HomeServiceImpl implements HomeService {

    private final SysWebConfigMapper sysWebConfigMapper;

    private final RedisUtil redisUtil;

    private final SysNoticeMapper noticeMapper;

    private final SysUserMapper userMapper;

    private final SysArticleMapper articleMapper;

    /**
     * 初始化Redis计数器
     */
    @PostConstruct
    public void initCounters() {
        // 初始化总访问量计数器（如果不存在）
        if (!redisUtil.hasKey(RedisConstants.UNIQUE_VISITOR_COUNT)) {
            redisUtil.set(RedisConstants.UNIQUE_VISITOR_COUNT, 0);
            System.out.println("初始化总访问量计数器");
        }

        // 初始化总浏览量计数器（如果不存在）
        if (!redisUtil.hasKey(RedisConstants.BLOG_VIEWS_COUNT)) {
            redisUtil.set(RedisConstants.BLOG_VIEWS_COUNT, 0);
            System.out.println("初始化总浏览量计数器");
        }

        // 初始化今日访问量计数器（如果不存在）
        String today = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        String todayVisitorCountKey = RedisConstants.TODAY_VISITOR_COUNT + ":" + today;
        if (!redisUtil.hasKey(todayVisitorCountKey)) {
            redisUtil.set(todayVisitorCountKey, 0);
            System.out.println("初始化今日访问量计数器: " + todayVisitorCountKey);
        }
    }

    @Override
    public Result<SysWebConfig> getWebConfig() {

        SysWebConfig sysWebConfig = new SysWebConfig();

        // 先从数据库获取最新的配置信息
        LambdaQueryWrapper<SysWebConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.last("limit 1");
        SysWebConfig dbConfig = sysWebConfigMapper.selectOne(wrapper);

        if (dbConfig != null) {
            // 计算数据库配置的哈希值，用于检测数据变化
            String dbConfigHash = calculateConfigHash(dbConfig);

            // 检查Redis缓存
            Object value = redisUtil.get(RedisConstants.WEB_CONFIG_KEY);
            Object cachedHashValue = redisUtil.get(RedisConstants.WEB_CONFIG_KEY + ":hash");

            if (value != null && cachedHashValue != null) {
                String cachedHash = cachedHashValue.toString();

                // 比较哈希值，判断数据是否发生变化
                if (!dbConfigHash.equals(cachedHash)) {
                    // 数据库数据发生变化，需要同步到Redis缓存
                    redisUtil.set(RedisConstants.WEB_CONFIG_KEY, JSONObject.toJSONString(dbConfig),
                            RedisConstants.DAY_EXPIRE);
                    redisUtil.set(RedisConstants.WEB_CONFIG_KEY + ":hash", dbConfigHash,
                            RedisConstants.DAY_EXPIRE);
                    sysWebConfig = dbConfig;
                    System.out.println("检测到数据库配置变化（哈希值不匹配），已同步到Redis缓存");
                } else {
                    // 数据未变化，使用缓存数据
                    sysWebConfig = JSONObject.parseObject(value.toString(), SysWebConfig.class);
                    System.out.println("Redis缓存数据一致不需要同步");
                }
            } else {
                // 缓存不存在，将数据库配置存入Redis
                redisUtil.set(RedisConstants.WEB_CONFIG_KEY, JSONObject.toJSONString(dbConfig),
                        RedisConstants.DAY_EXPIRE);
                redisUtil.set(RedisConstants.WEB_CONFIG_KEY + ":hash", dbConfigHash,
                        RedisConstants.DAY_EXPIRE);
                sysWebConfig = dbConfig;
                System.out.println("缓存不存在，已将数据库配置存入Redis");
            }
        }

        // 获取浏览量和访问量
        long blogViewsCount = 0;
        long visitorCount = 0;
        long todayVisitorCount = 0;

        // 确保即使Redis中没有数据，也返回0
        if (redisUtil.hasKey(RedisConstants.BLOG_VIEWS_COUNT)) {
            Object blogViewsObj = redisUtil.get(RedisConstants.BLOG_VIEWS_COUNT);
            if (blogViewsObj != null) {
                blogViewsCount = Long.parseLong(blogViewsObj.toString());
            }
        }

        if (redisUtil.hasKey(RedisConstants.UNIQUE_VISITOR_COUNT)) {
            Object visitorObj = redisUtil.get(RedisConstants.UNIQUE_VISITOR_COUNT);
            if (visitorObj != null) {
                visitorCount = Long.parseLong(visitorObj.toString());
            }
        }

        // 获取今日访问量
        String today = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        String todayVisitorKey = RedisConstants.TODAY_VISITOR_COUNT + ":" + today;
        if (redisUtil.hasKey(todayVisitorKey)) {
            Object todayVisitorObj = redisUtil.get(todayVisitorKey);
            if (todayVisitorObj != null) {
                todayVisitorCount = Long.parseLong(todayVisitorObj.toString());
            }
        }

        // 记录日志
        System.out.println(
                "获取访问量数据 - 今日访问量: " + todayVisitorCount + ", 总访问量: " + visitorCount + ", 总浏览量: " + blogViewsCount);

        return Result
                .success(sysWebConfig)
                .putExtra("blogViewsCount", blogViewsCount)
                .putExtra("visitorCount", visitorCount)
                .putExtra("todayVisitorCount", todayVisitorCount);
    }

    @Override
    public JSONObject getHotSearch(String type) {
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("access-key", "f94be500c45148bc185be24a38c04ad3");
        paramMap.put("secret-key", "27563ca627d5db0d57e831ca4de0f75f");
        String url = "https://www.coderutil.com/api/resou/v1/" + type;
        String result = HttpUtil.get(url, paramMap);
        return JSONObject.parseObject(result);
    }

    @Override
    public void report() {
        try {
            // 获取ip
            String ipAddress = IpUtil.getIp();
            System.out.println("用户访问IP: " + ipAddress);

            // 通过浏览器解析工具类UserAgent获取访问设备信息
            UserAgent userAgent = IpUtil.getUserAgent(Objects.requireNonNull(IpUtil.getRequest()));
            Browser browser = userAgent.getBrowser();
            OperatingSystem operatingSystem = userAgent.getOperatingSystem();

            // 生成唯一用户标识
            String uuid = ipAddress + browser.getName() + operatingSystem.getName();
            String md5 = DigestUtils.md5DigestAsHex(uuid.getBytes());
            System.out.println("用户标识: " + md5);

            // 获取当前日期作为key的一部分
            String today = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
            String todayVisitorKey = RedisConstants.TODAY_VISITOR + ":" + today;
            String todayVisitorCountKey = RedisConstants.TODAY_VISITOR_COUNT + ":" + today;

            // 判断是否今日首次访问
            boolean isTodayFirstVisit = !redisUtil.sIsMember(todayVisitorKey, md5);
            System.out.println("是否今日首次访问: " + isTodayFirstVisit);

            if (isTodayFirstVisit) {
                // 今日访客量+1
                Long todayNewCount = redisUtil.increment(todayVisitorCountKey, 1);
                // 保存今日唯一访客标识
                redisUtil.sAdd(todayVisitorKey, md5);
                // 设置今日访客记录的过期时间（48小时后过期，确保能跨天）
                if (!redisUtil.hasKey(todayVisitorKey + ":ttl")) {
                    redisUtil.expire(todayVisitorKey, 48 * 60 * 60);
                    redisUtil.set(todayVisitorKey + ":ttl", "1", 48 * 60 * 60);
                }
                System.out.println("今日访问量增加为: " + todayNewCount);
            }

            // 判断是否总访问
            boolean isFirstVisit = !redisUtil.sIsMember(RedisConstants.UNIQUE_VISITOR, md5);
            System.out.println("是否首次访问网站: " + isFirstVisit);

            if (isFirstVisit) {
                // 访客量+1
                Long newCount = redisUtil.increment(RedisConstants.UNIQUE_VISITOR_COUNT, 1);
                // 保存唯一标识
                redisUtil.sAdd(RedisConstants.UNIQUE_VISITOR, md5);
                System.out.println("总访问量增加为: " + newCount);
            }

            // 访问量+1
            Long viewCount = redisUtil.increment(RedisConstants.BLOG_VIEWS_COUNT, 1);
            System.out.println("总浏览量增加为: " + viewCount);

        } catch (Exception e) {
            System.err.println("报告访问时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public Map<String, List<SysNotice>> getNotice() {

        List<SysNotice> sysNotices = noticeMapper.selectList(new LambdaQueryWrapper<SysNotice>()
                .eq(SysNotice::getIsShow, Constants.YES));
        return sysNotices.stream()
                .collect(Collectors.groupingBy(SysNotice::getPosition));
    }

    @Override
    public List<ContributionRankVO> getContributionRank() {
        // 创建存储贡献度排行结果的列表
        List<ContributionRankVO> resultList = new ArrayList<>();

        try {
            // 获取所有用户
            List<SysUser> users = userMapper.selectList(null);

            // 遍历用户并计算贡献度
            for (SysUser user : users) {
                // 获取用户所有已发布的文章
                List<SysArticle> publishedArticles = articleMapper.selectList(
                        new LambdaQueryWrapper<SysArticle>()
                                .eq(SysArticle::getUserId, user.getId())
                                .eq(SysArticle::getStatus, 1) // 1 表示已发布
                );

                // 如果用户没有发表文章，则跳过
                if (publishedArticles.isEmpty()) {
                    continue;
                }

                long articleCount = publishedArticles.size();

                // 计算总浏览量和总点赞数
                int totalViews = publishedArticles.stream()
                        .mapToInt(article -> article.getQuantity() == null ? 0 : article.getQuantity())
                        .sum();
                int totalLikes = publishedArticles.stream()
                        .mapToInt(article -> article.getLikeCount() == null ? 0 : article.getLikeCount())
                        .sum();

                // 创建贡献度VO对象
                ContributionRankVO contributionRankVO = new ContributionRankVO();
                contributionRankVO.setId(user.getId().longValue());
                contributionRankVO.setNickname(user.getNickname());
                contributionRankVO.setAvatar(user.getAvatar());
                contributionRankVO.setArticleCount((int) articleCount);
                contributionRankVO.setTotalViews(totalViews);
                contributionRankVO.setTotalLikes(totalLikes);

                // 计算贡献度得分：文章数 * 10 + 浏览量 * 0.1 + 点赞数 * 1
                double score = articleCount * 10 + totalViews * 0.1 + totalLikes;

                contributionRankVO.setContributionScore((int) score);

                resultList.add(contributionRankVO);
            }

            // 按贡献度得分降序排序
            resultList.sort((o1, o2) -> o2.getContributionScore().compareTo(o1.getContributionScore()));

            // 设置排名
            for (int i = 0; i < resultList.size(); i++) {
                resultList.get(i).setRank(i + 1);
            }

            // 只返回前10名
            return resultList.stream().limit(10).collect(Collectors.toList());

        } catch (Exception e) {
            e.printStackTrace();
            // 发生异常时也返回空列表，而不是null
            return new ArrayList<>();
        }
    }

    /**
     * 计算网站配置的哈希值，用于检测数据变化
     *
     * @param config 网站配置对象
     * @return 配置数据的MD5哈希值
     */
    private String calculateConfigHash(SysWebConfig config) {
        try {
            // 将配置对象转换为字符串，排除时间字段以避免时间格式影响
            StringBuilder sb = new StringBuilder();
            sb.append(config.getId()).append("|")
              .append(config.getLogo()).append("|")
              .append(config.getName()).append("|")
              .append(config.getSummary()).append("|")
              .append(config.getRecordNum()).append("|")
              .append(config.getWebUrl()).append("|")
              .append(config.getAuthor()).append("|")
              .append(config.getAuthorInfo()).append("|")
              .append(config.getAuthorAvatar()).append("|")  // 头像字段
              .append(config.getAliPay()).append("|")
              .append(config.getWeixinPay()).append("|")
              .append(config.getGithub()).append("|")
              .append(config.getGitee()).append("|")
              .append(config.getQqNumber()).append("|")
              .append(config.getQqGroup()).append("|")
              .append(config.getEmail()).append("|")
              .append(config.getWechat()).append("|")
              .append(config.getShowList()).append("|")
              .append(config.getLoginTypeList()).append("|")
              .append(config.getOpenComment()).append("|")
              .append(config.getOpenAdmiration()).append("|")
              .append(config.getTouristAvatar()).append("|")
              .append(config.getBulletin()).append("|")
              .append(config.getAboutMe()).append("|")
              .append(config.getOpenLantern()).append("|")
              .append(config.getOpenWatermark()).append("|")
              .append(config.getWatermarkText());

            // 计算MD5哈希值
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(sb.toString().getBytes(StandardCharsets.UTF_8));

            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            System.err.println("计算配置哈希值时发生错误: " + e.getMessage());
            // 如果计算哈希值失败，返回当前时间戳作为备用方案
            return String.valueOf(System.currentTimeMillis());
        }
    }
}
