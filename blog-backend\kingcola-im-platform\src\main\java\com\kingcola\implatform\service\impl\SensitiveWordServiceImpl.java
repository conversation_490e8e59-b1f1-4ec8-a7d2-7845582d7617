package com.kingcola.implatform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kingcola.entity.SensitiveWord;
import com.kingcola.implatform.service.SensitiveWordService;
import com.kingcola.mapper.SensitiveWordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SensitiveWordServiceImpl extends ServiceImpl<SensitiveWordMapper, SensitiveWord> implements SensitiveWordService {

    /**
     * 查询所有启用的敏感词内容
     * 注意：此方法对应的SensitiveWordMapper的日志级别已在application.yml中配置为OFF，
     * 使用SLF4J日志实现完全屏蔽敏感词查询的SQL日志输出，避免400多条敏感词内容在控制台日志中显示
     *
     * @return 启用的敏感词内容列表
     */
    @Override
    public List<String> findAllEnabledWords() {
        // 构建查询条件：只查询启用状态的敏感词，且只选择content字段
        LambdaQueryWrapper<SensitiveWord> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SensitiveWord::getEnabled, true);
        wrapper.select(SensitiveWord::getContent);

        // 执行查询并转换为字符串列表
        List<SensitiveWord> words = this.list(wrapper);
        return words.stream()
                .map(SensitiveWord::getContent)
                .collect(Collectors.toList());
    }
}
