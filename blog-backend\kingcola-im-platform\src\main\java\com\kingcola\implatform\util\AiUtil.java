package com.kingcola.implatform.util;

import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * @author: ynga
 * @date: 2025/3/28
 * @description:
 */
@Slf4j
@Component("aiUtilIMPlatform")
public class AiUtil {

    @Value("${ai.apiKey}")
    private String apiKey;

    @Value("${ai.baseUrl}")
    private String baseUrl;

    @Value("${ai.model}")
    private String model;

    private ArkService service = null;

    // 设置最大历史消息数，控制token消耗
    private static final int MAX_HISTORY_LENGTH = 4;

    @Data
    public static class AiResponse {
        private String content; // 最终回复内容
        private String reasoningContent; // 推理思考过程
        private long thinkingTimeMs; // 思考时间(毫秒)
        private int promptTokens; // 提示词token数
        private int completionTokens; // 回复token数
        private int totalTokens; // 总token数

        /*
         * public AiResponse(String content, String reasoningContent, long
         * thinkingTimeMs) {
         * this.content = content;
         * this.reasoningContent = reasoningContent;
         * this.thinkingTimeMs = thinkingTimeMs;
         * }
         */
        public AiResponse(String content, String reasoningContent, long thinkingTimeMs,
                int promptTokens, int completionTokens, int totalTokens) {
            this.content = content;
            this.reasoningContent = reasoningContent;
            this.thinkingTimeMs = thinkingTimeMs;
            this.promptTokens = promptTokens;
            this.completionTokens = completionTokens;
            this.totalTokens = totalTokens;
        }

        // 保留旧构造函数保持兼容性
        public AiResponse(String content, String reasoningContent, long thinkingTimeMs) {
            this(content, reasoningContent, thinkingTimeMs, 0, 0, 0);
        }
    }

    /**
     * AI助手角色设定
     * 1. 你是小K，KingCola-ICG大学生微科创新工作室的AI助手。
     * 2. 你主要擅长解答博客技术相关问题，包括但不限于编程语言、前后端开发、数据库、服务器架构等技术话题。
     * 3. 此外，你也可以回答用户的日常问题，但是要优先保持专业技术形象。
     * 4. 请记住，你是KingCola-ICG工作室的技术代表。
     */
    // 初始化AI角色设定
    public final List<ChatMessage> systemPrompt = new ArrayList<>(
            Collections.singletonList(ChatMessage.builder()
                    .role(ChatMessageRole.SYSTEM)
                    /*
                     * .content("你是小K，KingCola-ICG大学生微科创新工作室的AI助手。" +
                     * "你主要擅长解答博客技术相关问题，包括但不限于编程语言、前后端开发、数据库、服务器架构等技术话题。" +
                     * "此外，你也可以回答用户的日常问题，但是要优先保持专业技术形象。" +
                     * "请记住，你是KingCola-ICG工作室的技术代表。")
                     */
                    /*
                     * .content("你是小K，KingCola-ICG大学生微科创新工作室的AI助手。" +
                     * "擅长解答技术博客问题，同时能回答日常问题。" +
                     * "请基于对话历史进行连贯回复，关注上下文信息。" +
                     * "回答要简洁专业，必要时提供代码示例。" +
                     * "你是KingCola-ICG的技术代表。")
                     */
                    .content("你是小K，KingCola-ICG大学生微科创新工作室的AI助手。" +
                            "【用户XXX的提问】格式仅为内部标记，回复时不要引用或提及此格式。" +
                            "你主要擅长解答博客技术相关问题，包括但不限于编程语言、前后端开发、数据库、服务器架构等技术话题。" +
                            "另外，你必须始终记住并坚持自己的身份是小K，不是其他任何名称。" +
                            "你是作为KingCola-ICG工作室的技术代表的。")
                    .build()));

    private void initService() {
        if (service == null) {
            service = ArkService.builder()
                    .apiKey(apiKey)
                    // 因为deep-seek深度搜索时间较长，所以设置较长的等待时间
                    .timeout(Duration.ofMinutes(30))
                    .baseUrl(baseUrl)
                    .build();
        }
    }

    /**
     * 发送消息
     *
     * @param content 消息内容
     * @return 响应内容
     */
    /*
     * public String send(String content) {
     * initService();
     * // 初始化消息列表
     * List<ChatMessage> messages = new ArrayList<>();
     * // 创建用户消息
     * ChatMessage userMessage = ChatMessage.builder()
     * .role(ChatMessageRole.USER) // 设置消息角色为用户
     * .content(content) // 设置消息内容
     * .build();
     * // 将用户消息添加到消息列表
     * messages.add(userMessage);
     *
     * ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
     * .model(model)
     * .messages(messages)
     * .build();
     *
     * // 获取响应并收集每个选择的消息内容
     * List<Object> responses = new ArrayList<>();
     *
     * // 发送聊天完成请求并打印响应
     * try {
     * // 获取响应并打印每个选择的消息内容
     * service.createChatCompletion(chatCompletionRequest)
     * .getChoices()
     * .forEach(choice -> {
     * // 校验是否触发了深度思考，打印思维链内容
     * if (choice.getMessage().getReasoningContent() != null) {
     * System.out.println("推理内容: " + choice.getMessage().getReasoningContent());
     * } else {
     * System.out.println("推理内容为空");
     * }
     * // 打印消息内容
     * System.out.println("消息内容: " + choice.getMessage().getContent());
     * responses.add(choice.getMessage().getContent());
     * });
     * } catch (Exception e) {
     * System.out.println("请求失败: " + e.getMessage());
     * } finally {
     * // 关闭服务执行器
     * service.shutdownExecutor();
     * }
     * return responses.get(0).toString();
     * }
     */

    /**
     * 发送消息并返回AI响应(含推理过程和时间)
     *
     * @param content 消息内容
     * @return AI响应对象
     */
    public AiResponse send(String content) {
        return sendWithHistory(content, null);
        /*
         * initService();
         * // 创建消息列表，包含系统预设提示
         * List<ChatMessage> messages = new ArrayList<>(systemPrompt);
         * //List<ChatMessage> messages = new ArrayList<>();
         * ChatMessage userMessage = ChatMessage.builder()
         * .role(ChatMessageRole.USER)
         * .content(content)
         * .build();
         * messages.add(userMessage);
         *
         * ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
         * .model(model)
         * .messages(messages)
         * .build();
         *
         * long startTime = System.currentTimeMillis();
         * String responseContent = null;
         * String reasoningContent = null;
         * long thinkingTime = 0;
         * int promptTokens = 0;
         * int completionTokens = 0;
         * int totalTokens = 0;
         *
         * try {
         * var response = service.createChatCompletion(chatCompletionRequest);
         * var choices = response.getChoices();
         *
         * if (!choices.isEmpty()) {
         * var choice = choices.get(0);
         * responseContent = choice.getMessage().getContent()
         * .toString();
         * reasoningContent = choice.getMessage().getReasoningContent();
         * }
         *
         * // 获取token使用情况
         * if (response.getUsage() != null) {
         * promptTokens = (int)response.getUsage().getPromptTokens();
         * completionTokens = (int)response.getUsage().getCompletionTokens();
         * totalTokens = (int)response.getUsage().getTotalTokens();
         *
         * log.info("Token使用情况 - 提示词: {}, 回复: {}, 总计: {}",
         * promptTokens, completionTokens, totalTokens);
         * }
         * } catch (Exception e) {
         * log.error("AI调用异常", e);
         * responseContent = "抱歉，AI思考过程中遇到了问题: " + e.getMessage();
         * } finally {
         * // 记录结束时间
         * long endTime = System.currentTimeMillis();
         * thinkingTime = endTime - startTime;
         * // 关闭服务执行器
         * service.shutdownExecutor();
         * }
         * return new AiResponse(
         * responseContent != null ? responseContent : "无法获取回复",
         * reasoningContent != null ? reasoningContent : "",
         * thinkingTime
         * );
         */
    }

    /**
     * 发送消息并返回AI响应(含推理过程和时间)，同时保留历史消息
     *
     * @param content        消息内容
     * @param messageHistory 历史消息列表
     * @return
     */
    public AiResponse sendWithHistory(String content, List<ChatMessage> messageHistory) {
        initService();

        // 创建消息列表，首先加入系统提示
        List<ChatMessage> messages = new ArrayList<>(systemPrompt);

        // 添加有限的历史消息
        if (messageHistory != null && !messageHistory.isEmpty()) {
            // 如果历史过长，只保留最近的几条
            if (messageHistory.size() > MAX_HISTORY_LENGTH) {
                messageHistory = messageHistory.subList(
                        messageHistory.size() - MAX_HISTORY_LENGTH,
                        messageHistory.size());
            }
            messages.addAll(messageHistory);
        }

        // 添加当前用户消息
        ChatMessage userMessage = ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content(content)
                .build();
        messages.add(userMessage);

        // 剩余代码与原send方法相同
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .build();

        long startTime = System.currentTimeMillis();
        String responseContent = null;
        String reasoningContent = null;
        long thinkingTime = 0;
        int promptTokens = 0;
        int completionTokens = 0;
        int totalTokens = 0;

        try {
            var response = service.createChatCompletion(chatCompletionRequest);
            var choices = response.getChoices();

            if (!choices.isEmpty()) {
                var choice = choices.get(0);
                responseContent = choice.getMessage().getContent().toString();
                reasoningContent = choice.getMessage().getReasoningContent();
            }

            // 获取token使用情况
            if (response.getUsage() != null) {
                promptTokens = (int) response.getUsage().getPromptTokens();
                completionTokens = (int) response.getUsage().getCompletionTokens();
                totalTokens = (int) response.getUsage().getTotalTokens();

                log.info("Token使用情况 - 提示词: {}, 回复: {}, 总计: {}",
                        promptTokens, completionTokens, totalTokens);
            }
        } catch (Exception e) {
            log.error("AI调用异常", e);
            responseContent = "抱歉，AI思考过程中遇到了问题: " + e.getMessage();
        } finally {
            long endTime = System.currentTimeMillis();
            thinkingTime = endTime - startTime;
            service.shutdownExecutor();
        }
        return new AiResponse(
                responseContent != null ? responseContent : "无法获取回复",
                reasoningContent != null ? reasoningContent : "",
                thinkingTime,
                promptTokens,
                completionTokens,
                totalTokens);
    }

    /**
     * 流式回调接口
     */
    public interface StreamCallback {
        /**
         * 处理思考过程数据
         * @param thinkingContent 思考内容
         */
        void onThinking(String thinkingContent);

        /**
         * 处理回复内容数据
         * @param content 回复内容片段
         */
        void onContent(String content);

        /**
         * 处理完成事件
         * @param finalResponse 最终响应信息
         */
        void onComplete(AiResponse finalResponse);

        /**
         * 处理错误事件
         * @param error 错误信息
         */
        void onError(String error);
    }

    /**
     * 流式发送消息并返回AI响应
     *
     * @param content 消息内容
     * @param messageHistory 历史消息列表
     * @param callback 流式回调接口
     */
    public void sendWithHistoryStream(String content, List<ChatMessage> messageHistory, StreamCallback callback) {
        initService();

        // 创建消息列表，首先加入系统提示
        List<ChatMessage> messages = new ArrayList<>(systemPrompt);

        // 添加有限的历史消息
        if (messageHistory != null && !messageHistory.isEmpty()) {
            // 如果历史过长，只保留最近的几条
            if (messageHistory.size() > MAX_HISTORY_LENGTH) {
                messageHistory = messageHistory.subList(
                        messageHistory.size() - MAX_HISTORY_LENGTH,
                        messageHistory.size());
            }
            messages.addAll(messageHistory);
        }

        // 添加当前用户消息
        ChatMessage userMessage = ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content(content)
                .build();
        messages.add(userMessage);

        // 构建流式请求
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .stream(true) // 启用流式输出
                .build();

        long startTime = System.currentTimeMillis();
        StringBuilder responseContentBuilder = new StringBuilder();
        StringBuilder reasoningContentBuilder = new StringBuilder();
        final int[] promptTokens = {0};
        final int[] completionTokens = {0};
        final int[] totalTokens = {0};

        try {
            log.info("开始AI流式调用，内容：{}", content);

            // 使用流式调用
            service.streamChatCompletion(chatCompletionRequest)
                    .doOnError(throwable -> {
                        log.error("AI流式调用异常", throwable);
                        callback.onError("AI思考过程中遇到了问题: " + throwable.getMessage());
                    })
                    .blockingForEach(choice -> {
                        if (choice.getChoices() != null && !choice.getChoices().isEmpty()) {
                            var firstChoice = choice.getChoices().get(0);
                            var message = firstChoice.getMessage();

                            // 处理思考过程内容
                            if (message.getReasoningContent() != null && !message.getReasoningContent().isEmpty()) {
                                String thinkingContent = message.getReasoningContent();
                                reasoningContentBuilder.append(thinkingContent);
                                callback.onThinking(thinkingContent);
                                log.debug("AI思考过程片段：{}", thinkingContent);
                            }

                            // 处理回复内容
                            if (message.getContent() != null && !message.getContent().toString().isEmpty()) {
                                String contentPart = message.getContent().toString();
                                responseContentBuilder.append(contentPart);
                                callback.onContent(contentPart);
                                log.debug("AI回复内容片段：{}", contentPart);
                            }
                        }

                        // 获取token使用情况（如果有的话）
                        if (choice.getUsage() != null) {
                            promptTokens[0] = (int) choice.getUsage().getPromptTokens();
                            completionTokens[0] = (int) choice.getUsage().getCompletionTokens();
                            totalTokens[0] = (int) choice.getUsage().getTotalTokens();
                        }
                    });

            // 流式调用完成
            long endTime = System.currentTimeMillis();
            long thinkingTime = endTime - startTime;

            String finalContent = responseContentBuilder.toString();
            String finalReasoning = reasoningContentBuilder.toString();

            log.info("AI流式调用完成，总用时：{}ms，回复长度：{}，思考长度：{}",
                    thinkingTime, finalContent.length(), finalReasoning.length());

            if (promptTokens[0] > 0 || completionTokens[0] > 0 || totalTokens[0] > 0) {
                log.info("Token使用情况 - 提示词: {}, 回复: {}, 总计: {}",
                        promptTokens[0], completionTokens[0], totalTokens[0]);
            }

            // 创建最终响应
            AiResponse finalResponse = new AiResponse(
                    finalContent.isEmpty() ? "无法获取回复" : finalContent,
                    finalReasoning,
                    thinkingTime,
                    promptTokens[0],
                    completionTokens[0],
                    totalTokens[0]
            );

            callback.onComplete(finalResponse);

        } catch (Exception e) {
            log.error("AI流式调用异常", e);
            callback.onError("抱歉，AI思考过程中遇到了问题: " + e.getMessage());
        } finally {
            // 关闭服务执行器
            service.shutdownExecutor();
        }
    }
}
