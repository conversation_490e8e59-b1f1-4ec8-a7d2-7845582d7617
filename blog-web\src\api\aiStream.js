import request from '@/utils/request'
import { getToken } from '@/utils/cookie'

/**
 * AI流式对话API
 */
export default {
  /**
   * 创建AI流式对话EventSource连接
   * @param {Object} data 请求参数
   * @param {string} data.content 消息内容
   * @param {string} data.chatType 聊天类型：PRIVATE-私聊，GROUP-群聊
   * @param {number} data.receiverId 接收者ID（私聊时为用户ID，群聊时为群组ID）
   * @param {number} data.senderId 发送者ID
   * @param {string} data.senderNickname 发送者昵称
   * @returns {EventSource} SSE连接对象
   */
  createStreamChat(data) {
    // 构建查询参数
    const params = new URLSearchParams()
    Object.keys(data).forEach(key => {
      if (data[key] !== null && data[key] !== undefined) {
        params.append(key, data[key])
      }
    })

    // 获取token并添加到查询参数中（EventSource不支持自定义headers）
    const token = getToken()
    if (token) {
      params.append('token', token)
    }

    // 构建URL - 使用Vite环境变量
    const baseURL = import.meta.env.VITE_APP_BASE_API
    const url = `${baseURL}/ai/stream/chat?${params.toString()}`

    console.log('创建AI流式对话连接:', url)

    // 创建EventSource连接
    const eventSource = new EventSource(url)

    return eventSource
  },

  /**
   * 获取活跃连接数
   * @returns {Promise} 请求Promise
   */
  getActiveConnectionsCount() {
    return request({
      url: '/ai/stream/connections/count',
      method: 'get'
    })
  },

  /**
   * 关闭指定会话连接
   * @param {string} sessionId 会话ID
   * @returns {Promise} 请求Promise
   */
  closeConnection(sessionId) {
    return request({
      url: `/ai/stream/connections/${sessionId}/close`,
      method: 'post'
    })
  }
}
