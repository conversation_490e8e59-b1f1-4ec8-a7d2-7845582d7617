package com.kingcola.implatform.service;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * AI流式回复服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public interface AIStreamingService {

    /**
     * 处理私聊AI流式回复
     * 
     * @param messageId 消息ID
     * @param content 用户输入内容
     * @param userId 用户ID
     * @param emitter SSE发射器
     */
    void handlePrivateStreamingReply(Long messageId, String content, Long userId, SseEmitter emitter);

    /**
     * 处理群聊AI流式回复
     * 
     * @param messageId 消息ID
     * @param content 用户输入内容（已去除@小K）
     * @param groupId 群组ID
     * @param senderId 发送者ID
     * @param senderNickname 发送者昵称
     * @param emitter SSE发射器
     */
    void handleGroupStreamingReply(Long messageId, String content, Long groupId, Long senderId, String senderNickname, SseEmitter emitter);
}
