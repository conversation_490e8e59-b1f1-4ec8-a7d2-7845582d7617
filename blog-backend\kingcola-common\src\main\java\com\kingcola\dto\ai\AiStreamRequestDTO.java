package com.kingcola.dto.ai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * AI流式请求DTO
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@ApiModel(description = "AI流式请求DTO")
public class AiStreamRequestDTO {

    @ApiModelProperty(value = "消息内容", required = true)
    @NotBlank(message = "消息内容不能为空")
    private String content;

    @ApiModelProperty(value = "聊天类型：PRIVATE-私聊，GROUP-群聊", required = true)
    @NotBlank(message = "聊天类型不能为空")
    private String chatType;

    @ApiModelProperty(value = "接收者ID（私聊时为用户ID，群聊时为群组ID）", required = true)
    @NotNull(message = "接收者ID不能为空")
    private Long receiverId;

    @ApiModelProperty(value = "发送者ID", required = true)
    @NotNull(message = "发送者ID不能为空")
    private Long senderId;

    @ApiModelProperty(value = "发送者昵称")
    private String senderNickname;

    @ApiModelProperty(value = "会话ID，用于标识唯一的流式会话")
    private String sessionId;
}
