package com.kingcola.config.mybatisplus;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * MyBatis查询结果日志拦截器
 * 用于输出查询结果集，方便调试
 * 
 * <AUTHOR>
 */
@Intercepts({
    @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
public class ResultLogInterceptor implements Interceptor {

    private static final Logger logger = LoggerFactory.getLogger(ResultLogInterceptor.class);

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 执行原始查询
        Object result = invocation.proceed();
        
        // 获取MappedStatement以获取SQL ID
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        String sqlId = mappedStatement.getId();
        
        // 只有在DEBUG级别时才输出结果
        if (logger.isDebugEnabled()) {
            logQueryResult(sqlId, result);
        }
        
        return result;
    }

    /**
     * 记录查询结果
     *
     * @param sqlId SQL语句ID
     * @param result 查询结果
     */
    private void logQueryResult(String sqlId, Object result) {
        try {
            // 提取简化的方法名用于显示
            String methodName = extractMethodName(sqlId);

            if (result == null) {
                logger.debug("🔍 [{}] 查询结果: null", methodName);
                return;
            }

            if (result instanceof List) {
                List<?> list = (List<?>) result;
                if (list.isEmpty()) {
                    logger.debug("🔍 [{}] 查询结果: 空列表 []", methodName);
                } else {
                    logger.debug("🔍 [{}] 查询结果: 共 {} 条记录", methodName, list.size());

                    // 输出前几条记录的详细信息（最多3条，避免日志过多）
                    int maxDisplay = Math.min(list.size(), 3);
                    for (int i = 0; i < maxDisplay; i++) {
                        Object item = list.get(i);
                        String formattedItem = formatObject(item, "    ");
                        logger.debug("    📄 [{}] {}", i + 1, formattedItem);
                    }

                    // 如果记录数超过3条，显示省略信息
                    if (list.size() > 3) {
                        logger.debug("    ⋯ 还有 {} 条记录未显示", list.size() - 3);
                    }
                }
            } else {
                // 单个对象结果
                String formattedResult = formatObject(result, "");
                logger.debug("🔍 [{}] 查询结果: {}", methodName, formattedResult);
            }
        } catch (Exception e) {
            // 避免日志记录影响正常业务，捕获所有异常
            logger.warn("⚠️ 记录查询结果时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 提取方法名用于显示
     *
     * @param sqlId 完整的SQL ID
     * @return 简化的方法名
     */
    private String extractMethodName(String sqlId) {
        if (sqlId == null || sqlId.isEmpty()) {
            return "Unknown";
        }

        // 提取最后一个点后面的方法名
        int lastDotIndex = sqlId.lastIndexOf('.');
        if (lastDotIndex >= 0 && lastDotIndex < sqlId.length() - 1) {
            return sqlId.substring(lastDotIndex + 1);
        }

        return sqlId;
    }

    /**
     * 格式化对象输出
     *
     * @param obj 要格式化的对象
     * @param indent 缩进字符串
     * @return 格式化后的字符串
     */
    private String formatObject(Object obj, String indent) {
        if (obj == null) {
            return "null";
        }

        // 基本类型和包装类型
        if (obj instanceof String) {
            String str = (String) obj;
            if (str.length() > 100) {
                return "\"" + str.substring(0, 97) + "...\"";
            }
            return "\"" + str + "\"";
        }

        if (obj instanceof Number) {
            return obj.toString();
        }

        if (obj instanceof Boolean) {
            return obj.toString();
        }

        // 日期时间类型
        if (obj instanceof Date) {
            return "📅 " + obj.toString();
        }

        if (obj.getClass().getName().contains("LocalDateTime")) {
            return "🕐 " + obj.toString();
        }

        if (obj.getClass().getName().contains("LocalDate")) {
            return "📅 " + obj.toString();
        }

        // 集合类型
        if (obj instanceof Collection) {
            Collection<?> collection = (Collection<?>) obj;
            return String.format("📋 %s[%d]", obj.getClass().getSimpleName(), collection.size());
        }

        if (obj instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) obj;
            return String.format("🗂️ %s{%d}", obj.getClass().getSimpleName(), map.size());
        }

        // 实体对象 - 尝试智能格式化
        return formatEntityObject(obj, indent);
    }

    /**
     * 格式化实体对象
     *
     * @param obj 实体对象
     * @param indent 缩进字符串
     * @return 格式化后的字符串
     */
    private String formatEntityObject(Object obj, String indent) {
        String className = obj.getClass().getSimpleName();

        // 尝试获取对象的关键字段进行显示
        try {
            Map<String, Object> keyFields = extractKeyFields(obj);

            if (keyFields.isEmpty()) {
                return "📦 " + className + " 对象";
            }

            if (keyFields.size() == 1) {
                Map.Entry<String, Object> entry = keyFields.entrySet().iterator().next();
                return String.format("📦 %s{%s: %s}", className, entry.getKey(), formatSimpleValue(entry.getValue()));
            }

            // 多个字段时，格式化为多行显示
            StringBuilder sb = new StringBuilder();
            sb.append("📦 ").append(className).append(" {");

            List<Map.Entry<String, Object>> entries = new ArrayList<>(keyFields.entrySet());
            for (int i = 0; i < entries.size(); i++) {
                Map.Entry<String, Object> entry = entries.get(i);
                if (i == 0) {
                    sb.append(" ");
                } else {
                    sb.append(", ");
                }
                sb.append(entry.getKey()).append(": ").append(formatSimpleValue(entry.getValue()));
            }
            sb.append(" }");

            return sb.toString();

        } catch (Exception e) {
            // 如果反射失败，返回简单格式
            return "📦 " + className + " 对象";
        }
    }

    /**
     * 提取对象的关键字段
     *
     * @param obj 对象
     * @return 关键字段映射
     */
    private Map<String, Object> extractKeyFields(Object obj) {
        Map<String, Object> keyFields = new LinkedHashMap<>();

        try {
            // 获取所有字段
            Field[] fields = obj.getClass().getDeclaredFields();

            // 优先级字段列表
            List<String> priorityFields = Arrays.asList("id", "name", "title", "username", "nickname", "email", "status");

            // 首先查找优先级字段
            for (String priorityField : priorityFields) {
                for (Field field : fields) {
                    if (field.getName().equalsIgnoreCase(priorityField)) {
                        field.setAccessible(true);
                        Object value = field.get(obj);
                        if (value != null) {
                            keyFields.put(field.getName(), value);
                            if (keyFields.size() >= 3) { // 最多显示3个字段
                                return keyFields;
                            }
                        }
                        break;
                    }
                }
            }

            // 如果优先级字段不够，添加其他非空字段
            if (keyFields.size() < 3) {
                for (Field field : fields) {
                    if (keyFields.containsKey(field.getName())) {
                        continue; // 跳过已添加的字段
                    }

                    // 跳过某些不重要的字段
                    String fieldName = field.getName().toLowerCase();
                    if (fieldName.contains("password") || fieldName.contains("secret") ||
                        fieldName.contains("token") || fieldName.contains("createtime") ||
                        fieldName.contains("updatetime")) {
                        continue;
                    }

                    field.setAccessible(true);
                    Object value = field.get(obj);
                    if (value != null && !isComplexObject(value)) {
                        keyFields.put(field.getName(), value);
                        if (keyFields.size() >= 3) {
                            break;
                        }
                    }
                }
            }

        } catch (Exception e) {
            // 忽略反射异常
        }

        return keyFields;
    }

    /**
     * 判断是否为复杂对象
     *
     * @param value 值
     * @return 是否为复杂对象
     */
    private boolean isComplexObject(Object value) {
        return value instanceof Collection || value instanceof Map ||
               (!value.getClass().isPrimitive() &&
                !value.getClass().getName().startsWith("java.lang") &&
                !value.getClass().getName().startsWith("java.time") &&
                !value.getClass().getName().startsWith("java.math") &&
                !value.getClass().getName().startsWith("java.util.Date"));
    }

    /**
     * 格式化简单值
     *
     * @param value 值
     * @return 格式化后的字符串
     */
    private String formatSimpleValue(Object value) {
        if (value == null) {
            return "null";
        }

        if (value instanceof String) {
            String str = (String) value;
            if (str.length() > 50) {
                return "\"" + str.substring(0, 47) + "...\"";
            }
            return "\"" + str + "\"";
        }

        return value.toString();
    }

    @Override
    public Object plugin(Object target) {
        // 只拦截Executor类型的对象
        if (target instanceof Executor) {
            return Plugin.wrap(target, this);
        }
        return target;
    }

    @Override
    public void setProperties(Properties properties) {
        // 暂时不需要额外的配置属性
    }
}
