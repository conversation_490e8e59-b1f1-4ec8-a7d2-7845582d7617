package com.kingcola.config.mybatisplus;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Properties;

/**
 * MyBatis查询结果日志拦截器
 * 用于输出查询结果集，方便调试
 * 
 * <AUTHOR>
 */
@Intercepts({
    @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
public class ResultLogInterceptor implements Interceptor {

    private static final Logger logger = LoggerFactory.getLogger(ResultLogInterceptor.class);

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 执行原始查询
        Object result = invocation.proceed();
        
        // 获取MappedStatement以获取SQL ID
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        String sqlId = mappedStatement.getId();
        
        // 只有在DEBUG级别时才输出结果
        if (logger.isDebugEnabled()) {
            logQueryResult(sqlId, result);
        }
        
        return result;
    }

    /**
     * 记录查询结果
     * 
     * @param sqlId SQL语句ID
     * @param result 查询结果
     */
    private void logQueryResult(String sqlId, Object result) {
        try {
            if (result == null) {
                logger.debug("==> 查询结果: null (SQL ID: {})", sqlId);
                return;
            }

            if (result instanceof List) {
                List<?> list = (List<?>) result;
                if (list.isEmpty()) {
                    logger.debug("==> 查询结果: 空列表 [] (SQL ID: {})", sqlId);
                } else {
                    logger.debug("==> 查询结果: 共{}条记录 (SQL ID: {})", list.size(), sqlId);
                    
                    // 输出前几条记录的详细信息（最多5条，避免日志过多）
                    int maxDisplay = Math.min(list.size(), 5);
                    for (int i = 0; i < maxDisplay; i++) {
                        Object item = list.get(i);
                        logger.debug("    [{}] {}", i + 1, formatObject(item));
                    }
                    
                    // 如果记录数超过5条，显示省略信息
                    if (list.size() > 5) {
                        logger.debug("    ... 还有{}条记录未显示", list.size() - 5);
                    }
                }
            } else {
                // 单个对象结果
                logger.debug("==> 查询结果: {} (SQL ID: {})", formatObject(result), sqlId);
            }
        } catch (Exception e) {
            // 避免日志记录影响正常业务，捕获所有异常
            logger.warn("记录查询结果时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 格式化对象输出
     * 
     * @param obj 要格式化的对象
     * @return 格式化后的字符串
     */
    private String formatObject(Object obj) {
        if (obj == null) {
            return "null";
        }
        
        // 对于基本类型和字符串，直接返回
        if (obj instanceof String || obj instanceof Number || obj instanceof Boolean) {
            return obj.toString();
        }
        
        // 对于其他对象，使用toString()方法
        // 如果对象没有重写toString()，会显示类名和哈希码
        String objStr = obj.toString();
        
        // 如果toString()返回的是默认格式（类名@哈希码），尝试显示类名
        if (objStr.matches(".*@[a-fA-F0-9]+$")) {
            return obj.getClass().getSimpleName() + " 对象";
        }
        
        return objStr;
    }

    @Override
    public Object plugin(Object target) {
        // 只拦截Executor类型的对象
        if (target instanceof Executor) {
            return Plugin.wrap(target, this);
        }
        return target;
    }

    @Override
    public void setProperties(Properties properties) {
        // 暂时不需要额外的配置属性
    }
}
