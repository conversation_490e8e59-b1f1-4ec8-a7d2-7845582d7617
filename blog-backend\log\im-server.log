2025-08-01 16:27:44.817 [main] INFO  com.kingcola.imserver.BlogIMServerApplication - Starting BlogIMServerApplication using Java 11 on yuangang with PID 29432 (D:\WebProjects\Kingcola-Blog-System\blog-backend\kingcola-im-server\target\classes started by 35357 in D:\WebProjects\Kingcola-Blog-System\blog-backend)
2025-08-01 16:27:44.832 [main] INFO  com.kingcola.imserver.BlogIMServerApplication - The following 1 profile is active: "prod"
2025-08-01 16:27:47.450 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-01 16:27:47.454 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 16:27:47.503 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-08-01 16:27:48.355 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis.redisson-org.redisson.spring.starter.RedissonProperties' of type [org.redisson.spring.starter.RedissonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 16:27:48.372 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 16:27:48.379 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.redisson.spring.starter.RedissonAutoConfiguration' of type [org.redisson.spring.starter.RedissonAutoConfiguration$$EnhancerBySpringCGLIB$$e6fe6d2d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 16:27:48.627 [main] INFO  org.redisson.Version - Redisson 3.21.3
2025-08-01 16:27:54.892 [redisson-netty-2-7] INFO  o.r.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for 115.190.101.180/115.190.101.180:6379
2025-08-01 16:27:57.446 [redisson-netty-2-19] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for 115.190.101.180/115.190.101.180:6379
2025-08-01 16:27:57.577 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisson' of type [org.redisson.Redisson] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 16:27:57.616 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redissonConnectionFactory' of type [org.redisson.spring.data.connection.RedissonConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 16:27:57.619 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.kingcola.config.RedisConfig$$EnhancerBySpringCGLIB$$b9c1e46a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 16:27:59.898 [main] INFO  com.kingcola.imserver.BlogIMServerApplication - Started BlogIMServerApplication in 16.089 seconds (JVM running for 20.019)
2025-08-01 16:28:00.074 [main] INFO  com.kingcola.imserver.netty.ws.WebSocketServer - websocket server 初始化完成,端口：8878
