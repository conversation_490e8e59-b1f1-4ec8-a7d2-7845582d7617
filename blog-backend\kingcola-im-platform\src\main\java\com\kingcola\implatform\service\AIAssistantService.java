package com.kingcola.implatform.service;

import com.kingcola.dto.ai.AiStreamRequestDTO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * AI助手服务接口
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
public interface AIAssistantService {

    // 注意：非流式AI处理方法已移除，所有AI交互现在都通过流式SSE接口处理

    /**
     * 重置用户的AI对话历史
     * 
     * @param userId 用户ID
     */
    void resetChatHistory(Long userId);

    /**
     * 重置群组的AI对话历史
     *
     * @param groupId 群组ID
     */
    void resetGroupChatHistory(Long groupId);

    /**
     * 处理私聊AI流式回复
     * @param request 流式请求
     * @param emitter SSE发射器
     */
    void handlePrivateMessageStream(AiStreamRequestDTO request, SseEmitter emitter);

    /**
     * 处理群聊AI流式回复
     * @param request 流式请求
     * @param emitter SSE发射器
     */
    void handleGroupMessageStream(AiStreamRequestDTO request, SseEmitter emitter);
}
