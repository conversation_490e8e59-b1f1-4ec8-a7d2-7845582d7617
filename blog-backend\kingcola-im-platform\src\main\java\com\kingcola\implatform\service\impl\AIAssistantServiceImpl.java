package com.kingcola.implatform.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.kingcola.IMClient;
import com.kingcola.common.Constants;
import com.kingcola.dto.ai.AiStreamRequestDTO;
import com.kingcola.entity.PrivateMessage;
import com.kingcola.entity.GroupMessage;
import com.kingcola.entity.SysUser;
import com.kingcola.enums.IMTerminalType;
import com.kingcola.implatform.enums.MessageStatus;
import com.kingcola.implatform.enums.MessageType;
import com.kingcola.implatform.service.AIAssistantService;
import com.kingcola.implatform.service.GroupMemberService;
import com.kingcola.implatform.util.BeanUtils;
import com.kingcola.mapper.PrivateMessageMapper;
import com.kingcola.mapper.GroupMessageMapper;
import com.kingcola.mapper.SysUserMapper;
import com.kingcola.model.IMPrivateMessage;
import com.kingcola.model.IMGroupMessage;
import com.kingcola.model.IMUserInfo;
import com.kingcola.implatform.util.AiUtil;
import com.kingcola.utils.RedisUtil;
import com.kingcola.vo.ai.AiStreamResponseVO;
import com.kingcola.vo.im.PrivateMessageVO;
import com.kingcola.vo.im.GroupMessageVO;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * AI助手服务实现类
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AIAssistantServiceImpl implements AIAssistantService {

    private final IMClient imClient;
    private final AiUtil aiUtil;
    private final RedisUtil redisUtil;
    private final SysUserMapper sysUserMapper;
    private final PrivateMessageMapper privateMessageMapper;
    private final GroupMessageMapper groupMessageMapper;
    private final GroupMemberService groupMemberService;

    // 私聊会话历史保存的redis前缀
    private static final String PRIVATE_CHAT_HISTORY_KEY = "ai:private:history:";
    // 群聊会话历史保存的redis前缀
    private static final String GROUP_CHAT_HISTORY_KEY = "ai:group:history:";
    // 会话历史保存时间 (2小时)
    private static final long CHAT_HISTORY_TTL = 2 * 60 * 60;

    // 注意：非流式AI处理方法已移除，所有AI交互现在都通过流式SSE接口处理
    // 具体实现请参考 handlePrivateMessageStream() 和 handleGroupMessageStream() 方法

    // 注意：processPrivateAIReply 非流式方法已移除，现在使用 processPrivateAIStreamReply

    // 注意：processGroupAIReply 非流式方法已移除，现在使用 processGroupAIStreamReply

    /**
     * 发送私聊消息
     */
    private PrivateMessage sendPrivateMessage(Long recvId, String content) {
        // 保存消息到数据库
        PrivateMessage msg = new PrivateMessage();
        msg.setSendId(Constants.XIAO_ASSISTANT_ID);
        msg.setRecvId(recvId);
        msg.setContent(content);
        msg.setType(MessageType.TEXT.code());
        msg.setStatus(MessageStatus.UNSEND.code());
        msg.setSendTime(new Date());
        privateMessageMapper.insert(msg);

        // 通过IM系统发送消息
        PrivateMessageVO msgInfo = BeanUtils.copyProperties(msg, PrivateMessageVO.class);
        IMPrivateMessage<PrivateMessageVO> sendMessage = new IMPrivateMessage<>();
        sendMessage.setSender(new IMUserInfo(Constants.XIAO_ASSISTANT_ID, IMTerminalType.WEB.code()));
        sendMessage.setRecvId(recvId);
        sendMessage.setSendToSelf(false);
        sendMessage.setData(msgInfo);
        sendMessage.setSendResult(true);
        imClient.sendPrivateMessage(sendMessage);

        log.info("AI助手发送私聊消息，接收者：{}，内容：{}", recvId, content);
        return msg;
    }

    /**
     * 发送群聊消息
     */
    private GroupMessage sendGroupMessage(Long groupId, String content) {
        // 获取AI助手用户信息
        SysUser aiUser = sysUserMapper.selectById(Constants.XIAO_ASSISTANT_ID);
        
        // 保存消息到数据库
        GroupMessage msg = new GroupMessage();
        msg.setGroupId(groupId);
        msg.setSendId(Constants.XIAO_ASSISTANT_ID);
        msg.setSendNickName(aiUser != null ? aiUser.getNickname() : "小K");
        msg.setContent(content);
        msg.setType(MessageType.TEXT.code());
        msg.setSendTime(new Date());
        groupMessageMapper.insert(msg);

        // 通过IM系统发送群聊消息
        GroupMessageVO msgInfo = BeanUtils.copyProperties(msg, GroupMessageVO.class);

        // 获取群成员列表并发送消息
        try {
            // 获取群成员列表
            List<Long> memberIds = groupMemberService.findUserIdsByGroupId(groupId);

            IMGroupMessage<GroupMessageVO> sendMessage = new IMGroupMessage<>();
            sendMessage.setSender(new IMUserInfo(Constants.XIAO_ASSISTANT_ID, IMTerminalType.WEB.code()));
            sendMessage.setRecvIds(memberIds);
            sendMessage.setSendToSelf(false);
            sendMessage.setSendResult(true);
            sendMessage.setData(msgInfo);
            imClient.sendGroupMessage(sendMessage);
        } catch (Exception e) {
            log.error("AI助手发送群聊消息失败，群组：{}，内容：{}", groupId, content, e);
        }

        log.info("AI助手发送群聊消息，群组：{}，内容：{}", groupId, content);
        return msg;
    }

    /**
     * 更新私聊消息内容
     */
    private void updatePrivateMessage(Long messageId, String content) {
        PrivateMessage updateMsg = new PrivateMessage();
        updateMsg.setId(messageId);
        updateMsg.setContent(content);
        updateMsg.setSendTime(new Date()); // 更新为实际回复时间
        privateMessageMapper.updateById(updateMsg);

        // 重新发送更新后的消息
        PrivateMessage msg = privateMessageMapper.selectById(messageId);
        PrivateMessageVO msgInfo = BeanUtils.copyProperties(msg, PrivateMessageVO.class);
        IMPrivateMessage<PrivateMessageVO> sendMessage = new IMPrivateMessage<>();
        sendMessage.setSender(new IMUserInfo(Constants.XIAO_ASSISTANT_ID, IMTerminalType.WEB.code()));
        sendMessage.setRecvId(msg.getRecvId());
        sendMessage.setSendToSelf(false);
        sendMessage.setData(msgInfo);
        sendMessage.setSendResult(true);
        imClient.sendPrivateMessage(sendMessage);
    }

    /**
     * 更新群聊消息内容
     */
    private void updateGroupMessage(Long messageId, String content) {
        GroupMessage updateMsg = new GroupMessage();
        updateMsg.setId(messageId);
        updateMsg.setContent(content);
        updateMsg.setSendTime(new Date()); // 更新为实际回复时间
        groupMessageMapper.updateById(updateMsg);

        // 重新发送更新后的群聊消息
        GroupMessage msg = groupMessageMapper.selectById(messageId);
        GroupMessageVO msgInfo = BeanUtils.copyProperties(msg, GroupMessageVO.class);

        try {
            // 获取群成员列表
            List<Long> memberIds = groupMemberService.findUserIdsByGroupId(msg.getGroupId());

            IMGroupMessage<GroupMessageVO> sendMessage = new IMGroupMessage<>();
            sendMessage.setSender(new IMUserInfo(Constants.XIAO_ASSISTANT_ID, IMTerminalType.WEB.code()));
            sendMessage.setRecvIds(memberIds);
            sendMessage.setSendToSelf(false);
            sendMessage.setSendResult(true);
            sendMessage.setData(msgInfo);
            imClient.sendGroupMessage(sendMessage);
        } catch (Exception e) {
            log.error("重新发送群聊消息失败，消息ID：{}，内容：{}", messageId, content, e);
        }

        log.info("更新群聊消息，消息ID：{}，新内容：{}", messageId, content);
    }

    /**
     * 获取对话历史
     */
    private List<ChatMessage> getChatHistory(String historyKey) {
        List<ChatMessage> chatHistory = new ArrayList<>();
        Object historyObj = redisUtil.get(historyKey);
        if (historyObj != null) {
            chatHistory = JSON.parseObject(historyObj.toString(), new TypeReference<List<ChatMessage>>() {});
        }
        return chatHistory;
    }

    /**
     * 更新对话历史
     */
    private void updateChatHistory(String historyKey, String userContent, String aiContent) {
        List<ChatMessage> chatHistory = getChatHistory(historyKey);
        
        // 添加用户消息
        chatHistory.add(ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content(userContent)
                .build());
        
        // 添加AI回复
        chatHistory.add(ChatMessage.builder()
                .role(ChatMessageRole.ASSISTANT)
                .content(aiContent)
                .build());
        
        // 保存到Redis
        redisUtil.set(historyKey, JSON.toJSONString(chatHistory));
        redisUtil.expire(historyKey, CHAT_HISTORY_TTL, TimeUnit.SECONDS);
    }

    // 注意：sendErrorMessage 方法已移除，错误处理现在在流式方法中直接处理

    @Override
    public void resetChatHistory(Long userId) {
        String historyKey = PRIVATE_CHAT_HISTORY_KEY + userId;
        redisUtil.delete(historyKey);
        log.info("重置用户 {} 的AI对话历史", userId);
    }

    @Override
    public void resetGroupChatHistory(Long groupId) {
        String historyKey = GROUP_CHAT_HISTORY_KEY + groupId;
        redisUtil.delete(historyKey);
        log.info("重置群组 {} 的AI对话历史", groupId);
    }

    @Override
    public void handlePrivateMessageStream(AiStreamRequestDTO request, SseEmitter emitter) {
        String content = request.getContent();
        Long senderId = request.getSenderId();
        String senderNickname = request.getSenderNickname();
        String sessionId = request.getSessionId();

        log.info("处理私聊AI流式回复，发送者：{}，会话ID：{}，内容：{}", senderNickname, sessionId, content);

        // 异步处理流式回复
        ThreadUtil.execAsync(() -> {
            try {
                processPrivateAIStreamReply(content, senderId, senderNickname, sessionId, emitter);
            } catch (Exception e) {
                log.error("处理私聊AI流式回复异常", e);
                try {
                    sendSseMessage(emitter, AiStreamResponseVO.error(sessionId, "处理请求时发生错误: " + e.getMessage()));
                    emitter.complete();
                } catch (Exception ex) {
                    log.error("发送错误消息失败", ex);
                }
            }
        });
    }

    @Override
    public void handleGroupMessageStream(AiStreamRequestDTO request, SseEmitter emitter) {
        String content = request.getContent();
        Long senderId = request.getSenderId();
        String senderNickname = request.getSenderNickname();
        Long groupId = request.getReceiverId();
        String sessionId = request.getSessionId();

        log.info("处理群聊AI流式回复，发送者：{}，群组：{}，会话ID：{}，内容：{}",
                senderNickname, groupId, sessionId, content);

        // 异步处理流式回复
        ThreadUtil.execAsync(() -> {
            try {
                processGroupAIStreamReply(content, senderId, senderNickname, groupId, sessionId, emitter);
            } catch (Exception e) {
                log.error("处理群聊AI流式回复异常", e);
                try {
                    sendSseMessage(emitter, AiStreamResponseVO.error(sessionId, "处理请求时发生错误: " + e.getMessage()));
                    emitter.complete();
                } catch (Exception ex) {
                    log.error("发送错误消息失败", ex);
                }
            }
        });
    }

    /**
     * 处理私聊AI流式回复
     */
    private void processPrivateAIStreamReply(String content, Long senderId, String senderNickname,
                                           String sessionId, SseEmitter emitter) throws Exception {
        // 检查是否是重置记忆命令
        if ("重置记忆".equals(content) || "重置对话".equals(content)) {
            resetChatHistory(senderId);
            sendSseMessage(emitter, AiStreamResponseVO.content(sessionId, "我已经重置了我们之间的对话记忆，接下来我们可以开始新的对话了！"));
            sendSseMessage(emitter, AiStreamResponseVO.complete(sessionId, null, 0L, null));
            emitter.complete();
            return;
        }

        // 获取对话历史
        String historyKey = PRIVATE_CHAT_HISTORY_KEY + senderId;
        List<ChatMessage> chatHistory = getChatHistory(historyKey);

        // 创建消息记录（用于保存到数据库）
        PrivateMessage msg = new PrivateMessage();
        msg.setSendId(Constants.XIAO_ASSISTANT_ID);
        msg.setRecvId(senderId);
        msg.setContent(""); // 初始为空，后续更新
        msg.setType(MessageType.TEXT.code());
        msg.setStatus(MessageStatus.UNSEND.code());
        msg.setSendTime(new Date());
        privateMessageMapper.insert(msg);

        final StringBuilder fullThinkingContent = new StringBuilder();
        final StringBuilder fullResponseContent = new StringBuilder();

        // 使用流式调用
        aiUtil.sendWithHistoryStream(content, chatHistory, new AiUtil.StreamCallback() {
            @Override
            public void onThinking(String thinkingContent) {
                try {
                    fullThinkingContent.append(thinkingContent);
                    sendSseMessage(emitter, AiStreamResponseVO.thinking(sessionId, thinkingContent));
                } catch (Exception e) {
                    log.error("发送思考过程失败", e);
                }
            }

            @Override
            public void onContent(String contentPart) {
                try {
                    fullResponseContent.append(contentPart);
                    sendSseMessage(emitter, AiStreamResponseVO.content(sessionId, contentPart));
                } catch (Exception e) {
                    log.error("发送内容片段失败", e);
                }
            }

            @Override
            public void onComplete(AiUtil.AiResponse finalResponse) {
                try {
                    // 更新对话历史
                    updateChatHistory(historyKey, content, finalResponse.getContent());

                    // 更新数据库中的消息内容
                    String finalContent = finalResponse.getContent();
                    if (StringUtils.isNotBlank(finalResponse.getReasoningContent())) {
                        finalContent = "📝 *思考过程 (" + (finalResponse.getThinkingTimeMs() / 1000.0) + "秒)*\n" +
                                "```\n" + finalResponse.getReasoningContent() + "\n```\n\n" +
                                "🤖 *回复*\n" + finalResponse.getContent();
                    }

                    updatePrivateMessage(msg.getId(), finalContent);

                    // 发送完成消息
                    AiStreamResponseVO.TokenUsage tokenUsage = AiStreamResponseVO.TokenUsage.builder()
                            .promptTokens(finalResponse.getPromptTokens())
                            .completionTokens(finalResponse.getCompletionTokens())
                            .totalTokens(finalResponse.getTotalTokens())
                            .build();

                    sendSseMessage(emitter, AiStreamResponseVO.complete(sessionId, msg.getId(),
                            finalResponse.getThinkingTimeMs(), tokenUsage));
                    emitter.complete();

                    log.info("私聊AI流式回复完成，用户：{}，用时：{}ms", senderNickname, finalResponse.getThinkingTimeMs());
                } catch (Exception e) {
                    log.error("完成私聊AI流式回复时发生错误", e);
                    onError("完成回复时发生错误: " + e.getMessage());
                }
            }

            @Override
            public void onError(String error) {
                try {
                    sendSseMessage(emitter, AiStreamResponseVO.error(sessionId, error));
                    emitter.complete();
                } catch (Exception e) {
                    log.error("发送错误消息失败", e);
                }
            }
        });
    }

    /**
     * 处理群聊AI流式回复
     */
    private void processGroupAIStreamReply(String content, Long senderId, String senderNickname,
                                         Long groupId, String sessionId, SseEmitter emitter) throws Exception {
        String replaceContent = content.replace("@小K", "").trim();

        if (StringUtils.isBlank(replaceContent)) {
            sendSseMessage(emitter, AiStreamResponseVO.error(sessionId, "@小K后无有效内容"));
            emitter.complete();
            return;
        }

        // 检查是否是重置记忆命令
        if ("重置记忆".equals(replaceContent) || "重置对话".equals(replaceContent)) {
            resetGroupChatHistory(groupId);
            sendSseMessage(emitter, AiStreamResponseVO.content(sessionId, "@" + senderNickname + " 我已经重置了这个群的对话记忆，接下来我们可以开始新的对话了！"));
            sendSseMessage(emitter, AiStreamResponseVO.complete(sessionId, null, 0L, null));
            emitter.complete();
            return;
        }

        // 获取群聊对话历史
        String historyKey = GROUP_CHAT_HISTORY_KEY + groupId;
        List<ChatMessage> chatHistory = getChatHistory(historyKey);

        // 添加用户标识到提问中
        String enhancedContent = "【用户 " + senderNickname + " 的提问】: " + replaceContent;

        // 获取AI助手用户信息
        SysUser aiUser = sysUserMapper.selectById(Constants.XIAO_ASSISTANT_ID);

        // 创建消息记录（用于保存到数据库）
        GroupMessage msg = new GroupMessage();
        msg.setGroupId(groupId);
        msg.setSendId(Constants.XIAO_ASSISTANT_ID);
        msg.setSendNickName(aiUser != null ? aiUser.getNickname() : "小K");
        msg.setContent(""); // 初始为空，后续更新
        msg.setType(MessageType.TEXT.code());
        msg.setSendTime(new Date());
        groupMessageMapper.insert(msg);

        final StringBuilder fullThinkingContent = new StringBuilder();
        final StringBuilder fullResponseContent = new StringBuilder();

        // 使用流式调用
        aiUtil.sendWithHistoryStream(enhancedContent, chatHistory, new AiUtil.StreamCallback() {
            @Override
            public void onThinking(String thinkingContent) {
                try {
                    fullThinkingContent.append(thinkingContent);
                    sendSseMessage(emitter, AiStreamResponseVO.thinking(sessionId, thinkingContent));
                } catch (Exception e) {
                    log.error("发送思考过程失败", e);
                }
            }

            @Override
            public void onContent(String contentPart) {
                try {
                    fullResponseContent.append(contentPart);
                    sendSseMessage(emitter, AiStreamResponseVO.content(sessionId, contentPart));
                } catch (Exception e) {
                    log.error("发送内容片段失败", e);
                }
            }

            @Override
            public void onComplete(AiUtil.AiResponse finalResponse) {
                try {
                    // 更新对话历史
                    updateChatHistory(historyKey, replaceContent, finalResponse.getContent());

                    // 更新数据库中的消息内容
                    String finalContent = "@" + senderNickname + " " + finalResponse.getContent();
                    if (StringUtils.isNotBlank(finalResponse.getReasoningContent())) {
                        finalContent = "@" + senderNickname + " " +
                                "\n\n📝 *思考过程 (" + (finalResponse.getThinkingTimeMs() / 1000.0) + "秒)*\n" +
                                "```\n" + finalResponse.getReasoningContent() + "\n```\n\n" +
                                "🤖 *回复*\n" + finalResponse.getContent();
                    }

                    updateGroupMessage(msg.getId(), finalContent);

                    // 发送完成消息
                    AiStreamResponseVO.TokenUsage tokenUsage = AiStreamResponseVO.TokenUsage.builder()
                            .promptTokens(finalResponse.getPromptTokens())
                            .completionTokens(finalResponse.getCompletionTokens())
                            .totalTokens(finalResponse.getTotalTokens())
                            .build();

                    sendSseMessage(emitter, AiStreamResponseVO.complete(sessionId, msg.getId(),
                            finalResponse.getThinkingTimeMs(), tokenUsage));
                    emitter.complete();

                    log.info("群聊AI流式回复完成，群组：{}，用户：{}，用时：{}ms",
                            groupId, senderNickname, finalResponse.getThinkingTimeMs());
                } catch (Exception e) {
                    log.error("完成群聊AI流式回复时发生错误", e);
                    onError("完成回复时发生错误: " + e.getMessage());
                }
            }

            @Override
            public void onError(String error) {
                try {
                    sendSseMessage(emitter, AiStreamResponseVO.error(sessionId, error));
                    emitter.complete();
                } catch (Exception e) {
                    log.error("发送错误消息失败", e);
                }
            }
        });
    }

    /**
     * 发送SSE消息
     */
    private void sendSseMessage(SseEmitter emitter, AiStreamResponseVO response) throws Exception {
        String jsonData = JSON.toJSONString(response);
        emitter.send(SseEmitter.event()
                .name("ai-response")
                .data(jsonData)
                .reconnectTime(3000));
        log.debug("发送SSE消息：{}", jsonData);
    }
}
