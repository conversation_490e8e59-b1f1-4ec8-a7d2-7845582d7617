package com.kingcola.vo.ai;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AI流式响应VO
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "AI流式响应VO")
public class AiStreamResponseVO {

    @ApiModelProperty(value = "响应类型：thinking-思考过程，content-回复内容，complete-完成标识，error-错误信息")
    private String type;

    @ApiModelProperty(value = "内容数据")
    private String data;

    @ApiModelProperty(value = "会话ID")
    private String sessionId;

    @ApiModelProperty(value = "消息ID（用于前端更新消息）")
    private Long messageId;

    @ApiModelProperty(value = "是否完成")
    private Boolean finished;

    @ApiModelProperty(value = "错误信息")
    private String error;

    @ApiModelProperty(value = "思考用时（毫秒）")
    private Long thinkingTimeMs;

    @ApiModelProperty(value = "Token使用情况")
    private TokenUsage tokenUsage;

    /**
     * Token使用情况
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TokenUsage {
        @ApiModelProperty(value = "提示词token数")
        private Integer promptTokens;

        @ApiModelProperty(value = "回复token数")
        private Integer completionTokens;

        @ApiModelProperty(value = "总token数")
        private Integer totalTokens;
    }

    /**
     * 创建思考过程响应
     */
    public static AiStreamResponseVO thinking(String sessionId, String thinkingContent) {
        return AiStreamResponseVO.builder()
                .type("thinking")
                .data(thinkingContent)
                .sessionId(sessionId)
                .finished(false)
                .build();
    }

    /**
     * 创建内容响应
     */
    public static AiStreamResponseVO content(String sessionId, String content) {
        return AiStreamResponseVO.builder()
                .type("content")
                .data(content)
                .sessionId(sessionId)
                .finished(false)
                .build();
    }

    /**
     * 创建完成响应
     */
    public static AiStreamResponseVO complete(String sessionId, Long messageId, Long thinkingTimeMs, TokenUsage tokenUsage) {
        return AiStreamResponseVO.builder()
                .type("complete")
                .sessionId(sessionId)
                .messageId(messageId)
                .finished(true)
                .thinkingTimeMs(thinkingTimeMs)
                .tokenUsage(tokenUsage)
                .build();
    }

    /**
     * 创建错误响应
     */
    public static AiStreamResponseVO error(String sessionId, String error) {
        return AiStreamResponseVO.builder()
                .type("error")
                .error(error)
                .sessionId(sessionId)
                .finished(true)
                .build();
    }
}
