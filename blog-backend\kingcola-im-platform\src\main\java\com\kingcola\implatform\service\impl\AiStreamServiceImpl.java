package com.kingcola.implatform.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.kingcola.dto.ai.AiStreamRequestDTO;
import com.kingcola.implatform.service.AIAssistantService;
import com.kingcola.implatform.service.AiStreamService;
import com.kingcola.vo.ai.AiStreamResponseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI流式响应服务实现类
 * 负责处理AI流式对话的业务逻辑和SSE连接管理
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiStreamServiceImpl implements AiStreamService {

    private final AIAssistantService aiAssistantService;
    
    // 存储活跃的SSE连接
    private final ConcurrentHashMap<String, SseEmitter> activeConnections = new ConcurrentHashMap<>();

    @Override
    public SseEmitter createStreamChat(String content, String chatType, Long receiverId, 
                                     Long senderId, String senderNickname, String sessionId, String token) {
        
        // 构建请求DTO
        AiStreamRequestDTO request = buildStreamRequest(content, chatType, receiverId, 
                                                       senderId, senderNickname, sessionId);
        
        // 验证token并设置用户ID
        validateTokenAndSetUserId(request, token);
        
        // 生成会话ID（如果没有提供的话）
        if (StrUtil.isBlank(request.getSessionId())) {
            request.setSessionId(UUID.randomUUID().toString());
        }
        
        log.info("创建AI流式对话，会话ID：{}，发送者：{}，内容：{}",
                request.getSessionId(), request.getSenderNickname(), request.getContent());

        // 创建SSE连接
        SseEmitter emitter = createSseEmitter(request.getSessionId());
        
        // 异步处理AI流式回复
        processStreamChat(request, emitter);

        return emitter;
    }

    @Override
    public int getActiveConnectionsCount() {
        return activeConnections.size();
    }

    @Override
    public boolean closeConnection(String sessionId) {
        SseEmitter emitter = activeConnections.get(sessionId);
        if (emitter != null) {
            try {
                sendSseMessage(emitter, AiStreamResponseVO.builder()
                        .type("closed")
                        .sessionId(sessionId)
                        .data("连接已关闭")
                        .finished(true)
                        .build());
                emitter.complete();
                log.info("手动关闭SSE连接，会话ID：{}", sessionId);
                return true;
            } catch (IOException e) {
                log.error("关闭SSE连接失败，会话ID：{}", sessionId, e);
                return false;
            }
        }
        return false;
    }

    @Override
    public void sendToSession(String sessionId, AiStreamResponseVO response) {
        SseEmitter emitter = activeConnections.get(sessionId);
        if (emitter != null) {
            try {
                sendSseMessage(emitter, response);
            } catch (IOException e) {
                log.error("向会话发送消息失败，会话ID：{}", sessionId, e);
                activeConnections.remove(sessionId);
            }
        }
    }

    @Override
    public SseEmitter getConnection(String sessionId) {
        return activeConnections.get(sessionId);
    }

    /**
     * 构建流式请求DTO
     */
    private AiStreamRequestDTO buildStreamRequest(String content, String chatType, Long receiverId,
                                                 Long senderId, String senderNickname, String sessionId) {
        AiStreamRequestDTO request = new AiStreamRequestDTO();
        request.setContent(content);
        request.setChatType(chatType);
        request.setReceiverId(receiverId);
        request.setSenderId(senderId);
        request.setSenderNickname(senderNickname);
        request.setSessionId(sessionId);
        return request;
    }

    /**
     * 验证token并设置用户ID
     */
    private void validateTokenAndSetUserId(AiStreamRequestDTO request, String token) {
        if (StrUtil.isNotBlank(token)) {
            try {
                // 通过token获取用户ID进行验证
                Object loginId = StpUtil.getLoginIdByToken(token);
                if (loginId == null) {
                    log.warn("AI流式对话token验证失败，token无效: {}", token);
                    throw new RuntimeException("token无效");
                }
                // 设置当前用户ID到请求中
                request.setSenderId(Long.valueOf(loginId.toString()));
                log.info("AI流式对话token验证成功，用户ID: {}", loginId);
            } catch (Exception e) {
                log.error("AI流式对话token验证异常: {}", e.getMessage());
                throw new RuntimeException("token验证失败");
            }
        } else {
            log.warn("AI流式对话缺少token参数");
            throw new RuntimeException("缺少认证token");
        }
    }

    /**
     * 创建SSE连接
     */
    private SseEmitter createSseEmitter(String sessionId) {
        // 创建SSE连接，设置超时时间为30分钟
        SseEmitter emitter = new SseEmitter(30 * 60 * 1000L);
        
        // 存储连接
        activeConnections.put(sessionId, emitter);
        
        // 设置连接完成和超时的回调
        emitter.onCompletion(() -> {
            log.info("SSE连接完成，会话ID：{}", sessionId);
            activeConnections.remove(sessionId);
        });

        emitter.onTimeout(() -> {
            log.warn("SSE连接超时，会话ID：{}", sessionId);
            activeConnections.remove(sessionId);
        });

        emitter.onError((ex) -> {
            log.error("SSE连接错误，会话ID：{}", sessionId, ex);
            activeConnections.remove(sessionId);
        });
        
        return emitter;
    }

    /**
     * 处理流式对话
     */
    private void processStreamChat(AiStreamRequestDTO request, SseEmitter emitter) {
        try {
            // 发送连接成功消息
            sendSseMessage(emitter, AiStreamResponseVO.builder()
                    .type("connected")
                    .sessionId(request.getSessionId())
                    .data("连接成功，开始AI对话...")
                    .finished(false)
                    .build());

            // 根据聊天类型处理不同的流式回复（统一使用大写）
            if ("PRIVATE".equals(request.getChatType())) {
                aiAssistantService.handlePrivateMessageStream(request, emitter);
            } else if ("GROUP".equals(request.getChatType())) {
                aiAssistantService.handleGroupMessageStream(request, emitter);
            } else {
                sendSseMessage(emitter, AiStreamResponseVO.error(request.getSessionId(), 
                        "不支持的聊天类型: " + request.getChatType()));
                emitter.complete();
            }
            
        } catch (Exception e) {
            log.error("处理AI流式对话异常，会话ID：{}", request.getSessionId(), e);
            try {
                sendSseMessage(emitter, AiStreamResponseVO.error(request.getSessionId(), "处理请求时发生错误"));
                emitter.complete();
            } catch (IOException ioException) {
                log.error("发送错误消息失败", ioException);
            }
        }
    }

    /**
     * 发送SSE消息
     */
    private void sendSseMessage(SseEmitter emitter, AiStreamResponseVO response) throws IOException {
        String jsonData = JSON.toJSONString(response);
        emitter.send(SseEmitter.event()
                .name("ai-response")
                .data(jsonData)
                .reconnectTime(3000));
        log.debug("发送SSE消息：{}", jsonData);
    }
}
